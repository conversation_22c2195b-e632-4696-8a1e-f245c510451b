
import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import config, { LOCALSTORAGE_SIDEBARSTATUS,LOCALSTORAGE_EXTERNALLOGINURL, LOCALSTORAGE_APPID } from '@/config'
import isInIcestark from '@ice/stark-app/lib/isInIcestark'
import StorageService from '@/utils/storage'
interface MenuItem {
  code: string
  name: string
  value?:string
  path?: string // 对应路由
}
export const useMenusStore = defineStore('menus-store', {
  state: () => {
    return {
      systemInfo: {} as Record<string,any>,
      externalLoginUrl: StorageService.getLocal(LOCALSTORAGE_EXTERNALLOGINURL, ''),// 外部异常登录地址
      menus: null as null | MenuItem[],// 左侧菜单
      dynamicRoutes: [] as RouteRecordRaw[], // 动态路由数据
      dynamicRoutesState: false, // 是否加载了动态路由
      sidebar: {
        collapsed: StorageService.getLocal(LOCALSTORAGE_SIDEBARSTATUS, true)
      },
      appId: StorageService.getLocal(LOCALSTORAGE_APPID, null)
    };
  },
  getters: {
    menusCode: (state: any) => {
      return state.menus?.map((x:MenuItem)=>x.code) ?? []
    },
    defaultMenus: (state: any) => {
      return state.menus?.[0] ?? {}
    },
    isTianda: (state: any) => {
      return config.tiandaAppIds.includes(state.appId) && !isInIcestark()
    },
    isGuangzhou: (state: any) => {
      return config.guangzhouAppIds.includes(state.appId) && !isInIcestark()
    },
    themeKey: (state: any) => {
      return state.isTianda ? 'tianda' :'default'
    }
  },
  actions: {
    setExternalLoginUrl(url: string) {
      this.externalLoginUrl =  url
      StorageService.setLocal(LOCALSTORAGE_EXTERNALLOGINURL, url)
    },
    setSystemInfo(obj: Record<string,any>) {
      this.systemInfo = obj ?? {}
    },
    setMenu(menus: null | MenuItem[]) {
      this.menus =  menus
    },
    setAppId(val: any) {
      this.appId =  val
      StorageService.setLocal(LOCALSTORAGE_APPID, val)
    },
    setDynamicRoutes(routes: RouteRecordRaw[] = []) {
      this.dynamicRoutes = routes
    },
    setDynamicRoutesState(state?: boolean) {
      this.dynamicRoutesState = !!state
    },
    toggleSidebar(val: boolean) {
      this.sidebar.collapsed = val
      StorageService.setLocal(LOCALSTORAGE_SIDEBARSTATUS, val)
    }
  }
})
