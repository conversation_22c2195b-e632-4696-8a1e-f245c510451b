<template>
  <div class="history-body">
    <template v-for="(group, key) in chatGroupData">
      <div v-if="group.length" class="list-group">
        <div class="com-font title">{{ formatGroupName(key) }}</div>
        <div class="list-items">
          <ChatItem
            v-for="chat in group"
            :chatId="chat.id"
            :title="chat.title"
            :key="chat.id"
            :timeType="key"
            :html-title="chat.htmlTitle"
            :last-active-time="chat.lastActiveTime"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import ChatItem from '@/views/chat/components/history/components/chat-item.vue'

interface Props {
  chatGroupData: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  chatGroupData: () => {
    return {}
  }
})

function formatGroupName(type: string) {
  switch (type) {
    case 'topping':
      return '置顶'
    case 'today':
      return '今日'
    case 'pastWeek':
      return '近七日'
    case 'pastYear':
      return '近一年'
    default:
      return '更久'
  }
}
</script>
<style scoped lang="scss">
.history-body {
  display: flex;
  flex-direction: column;
  gap: 32px;

  .list-group {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .com-font {
      font-family: Source Han Sans;
      font-weight: normal;
      line-height: normal;
    }

    .title {
      font-size: var(--font-12);
      color: var(--text-2);
    }

    .list-items {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
