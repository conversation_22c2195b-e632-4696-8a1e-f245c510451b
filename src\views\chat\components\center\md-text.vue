

<template>
  <div class="text-black text-wrap min-w-[20px]" :class="wrapClass">
    <div ref="textRef" class="leading-relaxed break-words">
      <div v-if="!inversion">
        <div v-if="!asRawText" class="markdown-body" :class="[loading ? 'text-loading' : '']" v-html="text"/>
        <div v-else class="whitespace-pre-wrap" :class="[loading ? 'text-loading' : '']" v-text="text" />
      </div>
      <div v-else class="whitespace-pre-wrap" :class="[loading ? 'text-loading' : '']" v-text="text" />
    </div>

    <a-image
        :src="previewer.src"
        :preview="{visible: previewer.visible, onVisibleChange: toggleVisible}"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, onUnmounted, onUpdated, reactive, ref } from 'vue'
import { copyToClip } from '@/utils/copy'
import MarkdownIt from 'markdown-it'
import mdKatex from '@traptitech/markdown-it-katex'
import mila from 'markdown-it-link-attributes'
import hljs from 'highlight.js'

interface Props {
  inversion?: boolean
  error?: boolean
  loading?: boolean
  asRawText?: boolean
  text?: string
  isShowQuote?: boolean// 是否显示引用按钮
}

const props = withDefaults(defineProps<Props>(), {
  inversion: false,
  error: false,
  loading: false,
  asRawText: false,
  text: '' ,
  isShowQuote: true
})

const emit = defineEmits(['openQuote', 'openQuoteCard'])

const textRef = ref<HTMLElement>()

const mdi = new MarkdownIt({
  html: true,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  }
})

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })

const wrapClass = computed(() => {
  return [props.inversion ? 'font-bold message-request' : 'w-full message-reply', { 'text-red-500': props.error }]
})

const text = computed(() => {
  let value = props.text ?? ''
  if(!value) return ''
// 替换带中文括号的链接
const urlRegex = /(（*)(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])(）)/ig
value =  value.replace(urlRegex, (match) => {
  // 返回替换后的超链接
  const matchText = match.replace('（','').replace('）','')
  return `（ ${matchText} ）`;
})
// 引用按钮匹配
  const btnTextArr: RegExpMatchArray | null = value.match(/\[\^.*?\^\]/g)
  if(btnTextArr && btnTextArr.length) {
    btnTextArr.map((item: any)=> {
      const str = `<a class="link-text" href="javascript:;" data-val="${item.replace('[^', '').replace('^]', '')}"><span class="link-text-quote">引用</span>”</a>`
      const btnHtml = props.isShowQuote ?  str : ''
      value = value.replace(item,btnHtml)
    })
  }
  const imageMatchArr = value.match(/\[#&images\[.*?\]images&#\]/g)
  if (imageMatchArr && imageMatchArr.length) {
    imageMatchArr.map((itemMatch: any)=> {
      value = value.replace(itemMatch, '')
      let images = itemMatch.replace('[#&images[', '').replace(']images&#]', '')?.split(',')
      let imagesHtml = '<div class="flow-images">'
      images.forEach((item: any) => imagesHtml = imagesHtml.concat(`<img class="preview-img" src="${item}">`))
      imagesHtml.concat('</div>')
      value = value.concat(imagesHtml)
    })

  }

  if (!props.asRawText) {
    return mdi.render(value.replace('```markdown\n', '').replace('\n```', ''))
  }
  return value
})
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach(btn => {
      btn.addEventListener('click', () => {
        const code = btn.parentElement?.nextElementSibling?.textContent
        if (code) {
          copyToClip(code).then(() => {
            btn.textContent = '复制成功'
            setTimeout(() => {
              btn.textContent = '复制代码'
            }, 1000)
          })
        }
      })
    })
  }
}

function removeClickEvents(className: string) {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll(className)
    copyBtn.forEach(btn => {
      btn.removeEventListener('click', () => null)
    })
  }
}
// 点击放大图片
const addPreviewImgEvents = ()=> {
  if(!textRef.value) return
  const elementArr = textRef.value.querySelectorAll('img')
  elementArr.forEach((ele: any) => {
    ele.addEventListener('click', () => {
      previewer.src = ele.src
      toggleVisible(true)
    })
  })
}

// 点击请求左侧法律法规、操作片段数据
const addQuoteEvents = () => {
  if (!textRef.value) return
  const elementArr = textRef.value.querySelectorAll('.link-text')
  elementArr.forEach((ele: any) => {
    ele.onclick = function() {
      const dataVal = ele.getAttribute('data-val')
      emit('openQuote', dataVal)
    }
  })
}

const init =()=> {
  addCopyEvents()
  addQuoteEvents()
  addPreviewImgEvents()
}
const clear = ()=> {
  removeClickEvents('.code-block-header__copy')
  removeClickEvents('.link-text')
  removeClickEvents('.preview-img')
}
onMounted(() => {
  init()
})

onUpdated(() => {
  init()
})

onUnmounted(() => {
  clear()
})

const previewer = reactive({
  visible: false,
  src: ''
})

function toggleVisible(val: boolean) {
  previewer.visible = val
}
</script>
<style lang="scss"  scoped>
.text-black {
  font-size: var(--font-16);
}
.markdown-body {
  background-color: transparent;
  font-family: Source Han Sans;
  font-size: var(--font-14);
  word-break: break-all;
  pre code,
  pre tt {
    line-height: 1.65;
  } 

  .highlight pre,
  pre {
    background-color: transparent;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: var(--text-2);

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: var(--success-7);
        }
      }
    }
  }
}

.text-loading > *:last-child {
  position: relative;
  display: inline-block;
}

.markdown-body.text-loading > *:last-child::after,
.whitespace-pre-wrap.text-loading::after {
  content: '|';
  position: absolute;
  right: -10px;
  animation: blinker 1.2s infinite steps(1, start);
}

:deep(.markdown-body) {
  ol,ul {
    white-space: normal;
  }
  ol {
    list-style: revert;
  }

  ul {
    list-style-type: disc;
  }

  code {
    text-wrap: wrap;
  }
  p,div{
    white-space: break-spaces;
  }
  .link-text {
    padding: 1px 4px;
    gap: 2px;
    background: var(--main-1-5);
    font-family: 思源黑体;
    font-size: var(--font-16);
    font-weight: bold;
    color: var(--main-6);
    text-decoration: none;
    white-space: nowrap;
    .link-text-quote {
      display: inline-block;
      font-family: Source Han Sans;
      font-weight: normal;
      transform: scale(0.75);
    }
  }

  .flow-images {
    width: 100%;
    height: 500px;
    overflow-y: hidden;
    padding-right: 8px;
  }

  .flow-images:hover{
    overflow-y: scroll;
  }
}

:deep(.ant-image){
  display: none;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

</style>
