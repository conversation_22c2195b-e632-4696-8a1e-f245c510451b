import { nextTick } from 'vue'
import { sendQuestionByEventsUrl, sendQuestionByEventsTiandaUrl } from '@/api/chat'
import { useChatHistoryStore, useMenusStore } from '@/store'
import sse from '@/services/sse'
export const useSse = (state: Record<string,any>, currentQaMap: Record<string,any>)=> {
  const chatHistoryStore = useChatHistoryStore()
  // 建立sse链接
  const sseFn = sse()
  const sseRequest= (data: Record<string,any>,  businessCode = 'AI_Chat') => {
    const menusStore = useMenusStore()
    const url = menusStore.isTianda ? sendQuestionByEventsTiandaUrl :sendQuestionByEventsUrl
    currentQaMap.value.controller = new AbortController()
    sseFn.start(
      url,
      {
        body: {
          ...data,
          businessCode
        },
        signal: currentQaMap.value.controller.signal
      },
      function(res: Record<string, any>){
        const { status, message } = res
        if(status === 'running') sseOnmessage(message)
        else if(status === 'noPermission') {
          currentQaMap.value.controller.abort()
          currentQaMap.value.loading = true
          state.isLoading = true
        }
        else if(status === 'close') sseOnClose()
        else sseOnError()
      }
    )
  }

  const sseOnClose =() =>{
    console.log('sse onclose...')
    //如果是新建对话，增加至对话列表
    if(state.chatId) {
      if (state.isNewChat) {
        if (!currentQaMap.value.sensitive) {
          // 如果新建对话合法
          chatHistoryStore.addNewChat(state.chatId, state.question)
        }
      }else {
        chatHistoryStore.updateChatTime(state.chatId)
      }
    }
    //如果当前会话非法
    if(currentQaMap.value.hasSensitiveStyle) {
      state.disableAllInput = true
      setTimeout(() => {
        state.disableAllInput = false
        currentQaMap.value.hasSensitiveStyle = false
      }, 3000)
    }
    // 定位到会话最新位置
    scrollToBottom()
    state.isLoading = false
    currentQaMap.value.loading = false
    currentQaMap.value.isFinish = true
  }
  const sseOnmessage = (dataJson: any)=>  {
    console.log('sse onmessage...')
    if(!dataJson) return
    state.chatId = dataJson.chatId
    state.questionId = dataJson.questionId
    const lastItemAnswerIndex = state.qaList.length-1
    if(state.qaList[lastItemAnswerIndex]?.answerId) {
      // 更新会话流式内容, 会话都是一对一
      if (!state.qaList[lastItemAnswerIndex]?.isTerminate) {
        // 如果敏感，清空回答，重新接收提示信息
        if (dataJson.sensitive) {
          if (!state.qaList[lastItemAnswerIndex].sensitive) {
            state.qaList[lastItemAnswerIndex].content = ''
          }
        }

        state.qaList[lastItemAnswerIndex].content += dataJson.content
        state.qaList[lastItemAnswerIndex].sensitive = dataJson.sensitive
        state.qaList[lastItemAnswerIndex].thinkingTime = dataJson.thinkingTime
      }
    }else {
      state.qaList[lastItemAnswerIndex] =  Object.assign({},{ ...dataJson,thinkingTime: dataJson.thinkingTime, hasSensitiveStyle:  dataJson.sensitive, question:state.question  })
    }
    // 定位到会话最新位置
    scrollToBottom()
  }
  const sseOnError= ()=> {
    console.log('sseOnError')
    currentQaMap.value.isError = true
    currentQaMap.value.isFinish = true
    currentQaMap.value.loading = false
    state.isLoading = false
    scrollToBottom()
  }
  /** 定位到会话最新位置*/
  const scrollToBottom = ()=> {
    nextTick(()=>{
      const ele = document.getElementById('QaRef')
      if(ele) {
        ele.scrollTop = ele.scrollHeight
      }
    })
  }
  return { sseRequest, sseOnClose, sseOnError, scrollToBottom }
}
