<template>
  <div class="header-container">
    <div class="title-container">
      <div class="title">历史会话</div>
      <div v-show="historyCount > 0" class="number">({{historyCount}})</div>
    </div>
    <div class="button-container">
      <Button type="text" size="small" class="!h-[20px] !w-[20px] !p-0 !border-0" @click="close">
        <template #icon>
          <SvgIcon
            icon="icon-history-close"
            class="!h-[20px] !w-[20px]"
            color="var(--fill-9)"
          />
        </template>
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Button } from 'ant-design-vue'
import {SvgIcon} from '@/components/'

const props = defineProps({
  historyCount: {
    default: 0,
    type: Number
  }
})

const emits = defineEmits(['close'])

function close(){
  emits('close')
}

</script>

<style lang="scss" scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  width: 100%;
  height: 26px;

  .title-container {
    display: flex;
    gap: 4px;

    .title {
      font-family: Source Han Sans;
      font-size: var(--font-18);
      font-weight: bold;
      line-height: normal;
      text-align: justify;
      letter-spacing: 0;
      color: var(--text-5);
    }

    .number {
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: normal;
      line-height: normal;
      display: flex;
      align-items: center;
      letter-spacing: 0;
      color: var(--fill-6);
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
  }

}
</style>
