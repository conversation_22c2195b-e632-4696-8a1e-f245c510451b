<template>
  <div class="w-full h-full base-font footer">
    <span>{{ systemInfo.footerVersion }}</span>
    <span class="sub-text">{{ systemInfo.footerVersionSub }}</span>
  </div>
</template>
<script setup lang="ts">
import { useMenusStore } from '@/store'
import { computed } from 'vue'
// 系统信息
const menusStore = useMenusStore()
const systemInfo = computed(() => menusStore.systemInfo)
</script>
<style scoped>
.footer {
  position: relative;
  height: 24px;
  font-family: Source Han Sans;
  font-size: var(--font-12);
  text-align: center;
  color: var(--text-2);
  padding-top: 8px;
}

.sub-text {
  padding-left: 24px;
}
</style>
