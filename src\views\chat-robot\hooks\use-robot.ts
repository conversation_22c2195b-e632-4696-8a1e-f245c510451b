import { apiRefreshToken, generateToken } from '@/api/login'
import { useMenusStore,useRobotStore } from '@/store'


export const useRobot = ()=> {
  const menusStore = useMenusStore()
  const robotStore = useRobotStore()
  window.addEventListener(
    'message',
    (e) => {
      if(e.data.businessCode){
        window.BOSSSOFT_COOKIE = {}
        window.BOSSSOFT_COOKIE.businessCode = e.data.businessCode
      }
      if (e.data.command == 'refreshToken') {
        authenticate(e.data)
      }else if(e.data.command == 'refreshConfig'){
        authenticate(e.data)
        // if(e.data.mode == 1){
        //   showHeader.value = true
        // }else {
        //   showHeader.value = false
        // }
      }
    },
    false
  )

  function authenticate(data){
    window.BOSSSOFT_COOKIE = {
      userId: data.userId,
      businessCode: data.businessCode,
      appId: data.appId,
      token: ''
    }
    menusStore.setAppId(data.appId)
    if(data.sign){
        window.BOSSSOFT_COOKIE.token = ''
      generateToken(data).then((res) => {
        window.BOSSSOFT_COOKIE.token = res.data.token
        window.parent.postMessage({
          'command': 'removeLoading'
        }, '*')
        refreshToken(Number(res.data.expireTime))
      }).catch(({errMsg}) => {
        window.parent.postMessage({
          'command': 'authenticateError',
          'errMsg': errMsg
        }, '*')
      })
    }else {
      window.BOSSSOFT_COOKIE.token = data.token
      window.parent.postMessage({
        'command': 'removeLoading'
      }, '*')
      refreshToken(7200)
    }
  }

  function refreshToken(expireTime){
    if(window.BOSSSOFT_COOKIE.token) robotStore.getDefaultQuestionList()
    setTimeout(() => {
      apiRefreshToken().then((res) => {
        const { data } =  res
        refreshToken(data.expireTime)
      }).catch((e) => {
        console.error(e)
      })
    }, (expireTime - 3600) * 1000)
  }
}
