import {defineStore} from 'pinia'
import moment from 'moment'

export const useChatHistoryStore = defineStore('chatHistory', {
  state: () => ({
    topping: [],
    today: [],
    pastWeek: [],
    pastYear: [],
    older: [],
    chatIdMap: {},
    initialized: false,
    lastUpdateChatId: null
  }),
  getters: {
    chatHistory(state: any) {
      if (!this.initialized) {
        return null
      }
      return {
        topping: this.topping,
        today: this.today,
        pastWeek: this.pastWeek,
        pastYear: this.pastYear,
        older: this.older
      }
    }
  },
  actions: {

    /**
     * 清空缓存
     */
    clearChatHistory() {
      this.topping = []
      this.today = []
      this.pastWeek = []
      this.pastYear = []
      this.older = []
      this.initialized = false
    },

    /**
     * 更新会话时间
     *
     * @param chatId
     */
    updateChatTime(chatId: string) {
      if(!this.initialized || this.lastUpdateChatId == chatId){
        return;
      }
      const chatItem = this.chatIdMap[chatId] ?? {}
      // console.log(chatItem)
      const lastActiveTime = chatItem.lastActiveTime
      const oldType = this.getTimeGroup(lastActiveTime);
      const newType = this.getTimeGroup(moment().format('YYYY-MM-DD HH:mm:ss'));
      const index = this[oldType].findIndex(o => {
        if(o.id == chatId){
          return true;
        }
        return false;
      })
      chatItem.lastActiveTime = moment().format('YYYY-MM-DD HH:mm:ss')
      if(oldType == newType){
        this[oldType][index] = chatItem
        this[oldType].sort((v1, v2) => {
          if (v1.lastActiveTime > v2.lastActiveTime) {
            return -1;
          }
          if (v1.lastActiveTime < v2.lastActiveTime) {
            return 1;
          }
          return 0;
        })
      }else {
        this[oldType].splice(index, 1)
        this[newType].unshift(chatItem)
      }
      this.lastUpdateChatId = chatId
    },

    /**
     * 插入会话
     * @param chatId
     * @param title
     */
    addNewChat(chatId: string, title: string) {
      console.log(chatId)
      console.log(this.initialized, chatId in this.chatIdMap)
      if (!this.initialized || chatId in this.chatIdMap) {
        return
      }
      if (title.length > 256) {
        title = title.substring(0, 256)
      }
      const item = {
        id: chatId,
        title: title,
        isTopping: 0,
        createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        lastActiveTime: moment().format('YYYY-MM-DD HH:mm:ss')
      }
      this.today.unshift(item)
      this.chatIdMap[chatId] = item
      console.log(this.chatIdMap[chatId])
      this.lastUpdateChatId = chatId
    },

    /**
     * 初始化数据
     * @param data
     */
    initChatHistory(data: Record<string,any>) {
      const {topping, today, pastWeek, pastYear, older} = data
      topping?.forEach((item :Record<string,any>) => this.chatIdMap[item.id] = item)
      today?.forEach((item :Record<string,any>) => this.chatIdMap[item.id] = item)
      pastWeek?.forEach((item :Record<string,any>) => this.chatIdMap[item.id] = item)
      pastYear?.forEach((item :Record<string,any>) => this.chatIdMap[item.id] = item)
      older?.forEach((item :Record<string,any>) => this.chatIdMap[item.id] = item)
      this.topping = topping
      this.today = today
      this.pastWeek = pastWeek
      this.pastYear = pastYear
      this.older = older
      this.initialized = true
    },

    /**
     * 更新会话标题
     *
     * @param type
     * @param chatId
     * @param title
     */
    updateChatTitle(type: string, chatId: string, title: string) {
      for (let index = 0; index < this[type].length; index++) {
        if (this[type][index].id == chatId) {
          const chat = this[type][index]
          chat.title = title
          break
        }
      }
    },

    /**
     * 置顶、取消置顶
     * @param type
     * @param chatId
     * @param isTop
     */
    updateChatTopStatus(type: string, chatId: string, isTop: boolean) {
      if(isTop){ // 置顶
        for (let index = 0; index < this[type].length; index++) {
          if (this[type][index].id == chatId) {
            const chat = this[type][index]
            this[type].splice(index, 1)
            this.topping.unshift(chat)
            break
          }
        }
      }else { // 取消置顶
        for (let index = 0; index < this[type].length; index++) {
          if (this[type][index].id == chatId) {
            const chat = this[type][index]
            this.topping.splice(index, 1)
            const newType = this.getTimeGroup(chat.lastActiveTime)
            this.insertTimeGroup(newType, chat)
            break
          }
        }
      }
    },

    /**
     * 删除会话
     *
     * @param type
     * @param chatId
     */
    deleteChat(type: string, chatId: string) {
      for (let index = 0; index < this[type].length; index++) {
        if (this[type][index].id == chatId) {
          this[type].splice(index, 1)
          break
        }
      }
      delete this.chatIdMap[chatId]
    },

    /**
     * 将数据插入到指定分组
     *
     * @param type
     * @param chatItem
     */
    insertTimeGroup(type: string, chatItem: Record<string,any>){
      const index = this[type].findIndex(o => {
        if(chatItem.lastActiveTime >= o.lastActiveTime){
          return true;
        }
        return false;
      })
      if(index != -1){
        this[type].splice(index, 0, chatItem)
      }else {
        this[type].push(chatItem)
      }
    },

    /**
     * 获取时间对应的分组
     *
     * @param dateString
     * @returns {string}
     */
    getTimeGroup(dateString: string) {
      const now = new Date();
      const pastDate = new Date(dateString);
      const diffInMs = now - pastDate;
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
      if (diffInDays === 0) {
        return 'today';
      } else if (diffInDays <= 7) {
        return 'pastWeek';
      } else if (diffInDays <= 365) {
        return 'pastYear';
      } else {
        return 'older';
      }
    }
  }
})
