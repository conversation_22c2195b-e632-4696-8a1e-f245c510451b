<template>
  <a-flex vertical class="com-app-menu" :class="!isCollapse ? '' : 'is-collapse'">
    <a-tooltip placement="right" :title="isCollapse ? '展开' : '收起'">
      <div
:class="isCollapse ? 'btn-toggle btn-toggle-expand' : 'btn-toggle btn-toggle-collapse'"
           @click.stop="toggleCollapsed">
      </div>
    </a-tooltip>

    <a-flex class="app">
      <img :src="loadThemeImg('logo.png')" alt="logo" width="32" height="32"/>
      <span class="title">{{ systemInfo.htmlTitle }}</span>
    </a-flex>
    <a-menu v-model:selected-keys="selectKey" class="menu layout-aside-menu" mode="inline" :inline-collapsed="isCollapse" @click="onClickMenu">
      <a-menu-item  :key="config.route.currentSystemCode" @click="onClickMenuItem()">
        <template #icon>
          <img :src="loadThemeImg('ai-chat.svg')" alt="icon"/>
        </template>
        <span> 智能问答</span>
      </a-menu-item>
    </a-menu>

    <LayoutUser :user-visible="isCollapse" />
  </a-flex>
</template>
<script setup lang="ts">
import { computed,ref  } from 'vue'
import config from '@/config'
import  { useMenusStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import { loadThemeImg } from '@/hooks/use-theme'
import LayoutUser from '@/layout/layout-user.vue'

const route = useRoute()
const router = useRouter()
const menusStore = useMenusStore()
const menus = menusStore.menus

const currentView = computed(()=>menus?.find(x=>route.fullPath?.startsWith(x.path)) ?? {})
const selectKey = ref(currentView.value.code? [currentView.value.code] : [])

const isCollapse = ref(menusStore.sidebar.collapsed)
const onClickMenu = ({ key }: {key:string})=> {
  selectKey.value = [key]
}

const  toggleCollapsed = () => {
  isCollapse.value = !isCollapse.value
  menusStore.toggleSidebar(isCollapse.value)
}

// 系统信息
const systemInfo = computed(()=> menusStore.systemInfo)

const onClickMenuItem = ()=> {
  router.push({name: 'Home'})
}
</script>
<style scoped lang="scss">
.com-app-menu {
  position: sticky;
  top: 0;
  flex-shrink: 0;
  // width: 256px;
  min-width: 256px;
  padding: 24px 20px;
  gap: 32px;
  background: var(--theme-bg-1);
  height: 100vh;
  z-index: 1;
  &.is-collapse {
    width: 80px;
    min-width: 80px;

    .app {

      .title {
        display: none;
      }
    }
  }
  .svg-logo {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
  }
  .btn-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 50%;
    width: 16px;
    height: 32px;
    margin-top: -16px;
    &::before {
      content: '';
      width: 4px;
      height: 20px;
      border-radius: var(--border-radius-2);
      background-color:  var(--theme-bg-3);
    }

  }

  .btn-toggle-expand:hover {
    background: var(--menu-expand) 100% no-repeat;
    &::before{
      display: none;
    }
  }

  .btn-toggle-collapse:hover {
    background: var(--menu-collapse) 100% no-repeat;
    &::before{
      display: none;
    }
  }

  .app {
    align-items: center;
    gap: 4px;

    .title {
      font-size: var(--font-16);
      color: var(--theme-text-1);
      font-weight: bold;
    }
  }

 :deep(.menu) {
    flex: 1;
    background: none;
    border: none !important;
    .ant-menu-item {
      display: flex;
      align-items: center;
      margin-left: 0;
      margin-right: 0;
      padding-left: 0  !important;
      padding-right: 0;
      .ant-menu-item-icon {
        min-width: unset;
        padding-left: 8px;
        width: 32px;
        height: 24px;
        color:var(---text-5);
      }
      &.is-active {
        .ant-menu-item-icon {
          color: var(---main-6);
        }
      }
    }
    .ant-menu-title-content {
      display: flex;
      font-weight: bold;
      color: var(--theme-text-1);
    }

    .ant-menu-item-selected {
      background-color: var(--theme-bg-2);
      color: var(--main-6);
      .ant-menu-item-icon {
        color: var(--main-6);
      }
      .ant-menu-title-content {
        color: var(--theme-text-1);
      }
    }
    &.ant-menu-inline-collapsed > .ant-menu-item {
      width: 40px;
      .ant-menu-title-content {
        display: none;
      }
    }
  }

}

</style>
