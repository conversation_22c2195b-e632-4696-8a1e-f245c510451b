<!-- 国库司 -->
<template>
  <div class="national-purse-item"  :class="{'has-sort': data.sortId}">
    <SortNumber v-if="data.sortId" :content="data.sortId"/>
    <div class="title">
      <span class="s1">{{ data.unit }}</span>
      <span v-if="data.code" class="s2">[留言编号：{{ data.code }}]</span>
      <span v-if="data.answerTime" class="s3">[{{ data.answerTime }}]</span>
    </div>

    <div class="q">{{ data.question }}</div>

    <div class="a">
      <div>回答：</div>
      {{ data.answer }}
    </div>

    <raw-website :data="website"/>
  </div>
</template>
<script setup lang="ts">
import {computed} from 'vue'
import RawWebsite from "@/views/chat/components/quote/raw-website.vue";
import {Website} from "@/typings/chat";
import SortNumber from '@/views/chat/components/quote/sort-number.vue'
type Props = {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})

const website = computed<Website>(() => {
  return {
    name: props.data.siteName,
    url: props.data.url,
  }
})
</script>
<style scoped lang="scss">
.national-purse-item {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-radius: var(--border-radius-12);
  background: rgba(var(--fill-rbg-7),0.5);
  padding: 16px;
  &.has-sort{
    padding:30px 16px 16px 16px;
  }
  .title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: 思源黑体;
    font-weight: normal;
    line-height: normal;
    //color: rgba(var(--fill-rbg-7),0.5);
    color: var(--text-4);

    .s1 {
      font-weight: bold;
    }

    .s2 {
      font-family: Source Han Sans;
      font-size:  var(--font-12);
      flex-grow: 1;
      text-align: right;
    }

    .s3 {
      font-family: Source Han Sans;
      font-size: var(--font-12);
    }
  }

  .q {
    font-family: Source Han Sans;
    font-weight: normal;
    line-height: 22px;
    color: var(--text-4);
  }

  .a {
    display: flex;
    flex-direction: column;
    border-radius: var(--border-radius-12);
    padding: 16px;
    background: var(--fill-2);

    font-family: Source Han Sans;
    font-weight: normal;
    line-height: 22px;
    color: var(--text-4);
    white-space: break-spaces;
    word-break: break-all;
  }
}
</style>
