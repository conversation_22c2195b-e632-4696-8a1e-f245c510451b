{"v": "5.6.10", "fr": 30, "ip": 0, "op": 80, "w": 100, "h": 60, "nm": "合成 4", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.193, "y": 1}, "o": {"x": 0.429, "y": 0.356}, "t": 0, "s": [10, 60, 0], "e": [90, 60, 0], "to": [4.153, 0, 0], "ti": [-9.181, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 30, "s": [90, 60, 0], "e": [90, 60, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.285, "y": 1}, "o": {"x": 0.672, "y": 0.562}, "t": 40, "s": [90, 60, 0], "e": [10, 60, 0], "to": [-13.333, 0, 0], "ti": [13.333, 0, 0]}, {"t": 70}], "ix": 2}, "a": {"a": 0, "k": [0, 6, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 2, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.074509806931, 0.235294118524, 0.909803926945, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 306, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "2-2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [30.312, 60, 0], "e": [11.625, 60, 0], "to": [-3.115, 0, 0], "ti": [3.115, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 14, "s": [11.625, 60, 0], "e": [11.625, 60, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53, "s": [11.625, 60, 0], "e": [30.312, 60, 0], "to": [3.115, 0, 0], "ti": [-3.115, 0, 0]}, {"t": 68}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [90, 110, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20, "s": [100, 100, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 67, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 71, "s": [90, 110, 100], "e": [100, 100, 100]}, {"t": 74}], "ix": 6}}, "ao": 0, "ip": 0, "op": 306, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [90], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53, "s": [90], "e": [0]}, {"t": 68}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 7, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [-0.312, -6, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 60.5, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"t": 68}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 2, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.23137255013, 0.40000000596, 0.960784316063, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 306, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "2-3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [50.312, 60, 0], "e": [31.625, 60, 0], "to": [-3.115, 0, 0], "ti": [3.115, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 18, "s": [31.625, 60, 0], "e": [31.625, 60, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [31.625, 60, 0], "e": [50.312, 60, 0], "to": [3.115, 0, 0], "ti": [-3.115, 0, 0]}, {"t": 65}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [120, 70, 100], "e": [95, 105, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21, "s": [95, 105, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 64, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 65, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68, "s": [90, 110, 100], "e": [100, 100, 100]}, {"t": 71}], "ix": 6}}, "ao": 0, "ip": -1, "op": 310, "st": 4, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "6", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [0], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [90], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [90], "e": [0]}, {"t": 65}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 11, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [-0.312, -6, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 57.5, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"t": 65}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 2, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.400000029919, 0.560784313725, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": -1, "op": 310, "st": 4, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 3, "nm": "2-4", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [70.312, 60, 0], "e": [51.625, 60, 0], "to": [-3.115, 0, 0], "ti": [3.115, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 22, "s": [51.625, 60, 0], "e": [51.625, 60, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 45, "s": [51.625, 60, 0], "e": [70.312, 60, 0], "to": [3.115, 0, 0], "ti": [-3.115, 0, 0]}, {"t": 60}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 25, "s": [90, 110, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [90, 110, 100], "e": [100, 100, 100]}, {"t": 66}], "ix": 6}}, "ao": 0, "ip": -2, "op": 314, "st": 8, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "7", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [90], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [90], "e": [0]}, {"t": 60}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 22, "s": [-0.312, -6, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 45, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.5, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"t": 60}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 2, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.560784313725, 0.690196078431, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": -2, "op": 314, "st": 8, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "2-5", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [90.188, 60, 0], "e": [71.5, 60, 0], "to": [-3.115, 0, 0], "ti": [3.115, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 27, "s": [71.5, 60, 0], "e": [71.5, 60, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [71.5, 60, 0], "e": [90.188, 60, 0], "to": [3.115, 0, 0], "ti": [-3.115, 0, 0]}, {"t": 54}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [90, 110, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 33, "s": [100, 100, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53, "s": [100, 100, 100], "e": [120, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 54, "s": [120, 70, 100], "e": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [90, 110, 100], "e": [100, 100, 100]}, {"t": 60}], "ix": 6}}, "ao": 0, "ip": -2, "op": 319, "st": 13, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "8", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [90], "e": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [90], "e": [0]}, {"t": 54}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 20, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [-0.312, -6, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.115, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [-0.312, -6, 0], "e": [-0.312, -30, 0], "to": [0, -4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.862, "y": 0}, "t": 47, "s": [-0.312, -30, 0], "e": [-0.312, -6, 0], "to": [0, 0, 0], "ti": [0, -4, 0]}, {"t": 54}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 2, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.721568627451, 0.811764765721, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": -2, "op": 319, "st": 13, "bm": 0}], "markers": []}