import interact from 'interactjs'

let tuoZhuaiImgDefault = 'data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBmaWxsPSJub25lIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDEwIDIwIj48ZGVmcz48Y2xpcFBhdGggaWQ9Im1hc3Rlcl9zdmcwXzMyMDFfMDM0NzYiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMCIgaGVpZ2h0PSIyMCIgcng9IjAiLz48L2NsaXBQYXRoPjwvZGVmcz48ZyBjbGlwLXBhdGg9InVybCgjbWFzdGVyX3N2ZzBfMzIwMV8wMzQ3NikiPjxnPjxyZWN0IHg9IjIiIHk9IjIiIHdpZHRoPSIyIiBoZWlnaHQ9IjE2IiByeD0iMSIgZmlsbD0iI0RBREJFMCIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjxnPjxyZWN0IHg9IjYiIHk9IjIiIHdpZHRoPSIyIiBoZWlnaHQ9IjE2IiByeD0iMSIgZmlsbD0iI0RBREJFMCIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjwvZz48L3N2Zz4='
let tuoZhuaiImgSelect = 'data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBmaWxsPSJub25lIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDEwIDIwIj48ZGVmcz48Y2xpcFBhdGggaWQ9Im1hc3Rlcl9zdmcwXzMyMDFfMDM0NzkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMCIgaGVpZ2h0PSIyMCIgcng9IjAiLz48L2NsaXBQYXRoPjwvZGVmcz48ZyBjbGlwLXBhdGg9InVybCgjbWFzdGVyX3N2ZzBfMzIwMV8wMzQ3OSkiPjxnPjxyZWN0IHg9IjIiIHk9IjIiIHdpZHRoPSIyIiBoZWlnaHQ9IjE2IiByeD0iMSIgZmlsbD0iIzAxMDIwNyIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjxnPjxyZWN0IHg9IjYiIHk9IjIiIHdpZHRoPSIyIiBoZWlnaHQ9IjE2IiByeD0iMSIgZmlsbD0iIzAxMDIwNyIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjwvZz48L3N2Zz4='

// 创建一个新的style元素
var style = document.createElement('style');

const time = new Date().getTime();
const chatWindowId = `chat-window${time}`;

// 定义CSS内容
style.innerHTML = `
    #${chatWindowId} .chat_robot_loading{
        height: 15px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        width: 100%;
        height: 100%;
        background: #fff;
        z-index: 100;
        display: flex;
        justify-content: center;
        align-items: center;
        // border-radius: 1.5rem 0px 0px 1.5rem;
        border-radius: 24px 0px 0px 24px;
    }
    #${chatWindowId} .chat_robot_loading span{
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 3px;
        background: #133ce8;
        -webkit-animation: load 1.04s ease infinite;
    }
    #${chatWindowId} .chat_robot_loading span:last-child{
        margin-right: 0px;
    }
    @-webkit-keyframes load{
        0%{
            opacity: 1;
        }
        100%{
            opacity: 0;
        }
    }
    #${chatWindowId} .chat_robot_loading span:nth-child(1){
        -webkit-animation-delay:0.13s;
    }
    #${chatWindowId} .chat_robot_loading span:nth-child(2){
        -webkit-animation-delay:0.26s;
    }
    #${chatWindowId} .chat_robot_loading span:nth-child(3){
        -webkit-animation-delay:0.39s;
    }
    #${chatWindowId} .chat_robot_loading span:nth-child(4){
        -webkit-animation-delay:0.52s;
    }
    #${chatWindowId} .chat_robot_loading span:nth-child(5){
        -webkit-animation-delay:0.65s;
    }

    #${chatWindowId} .alert-container {
      display: none;
      position: relative;
      background-color: white;
      text-align: center;
      top:50%;
      left:50%;
      border-radius: 5px;
      -webkit-transform: translate(-50%,-50%);
      -moz-transform: translate(-50%,-50%);
      transform:translate(-50%,-50%);
    }

    #${chatWindowId} .alert-image {
      margin: 0 auto;
      width: 50px;
      height: 50px;
      background-image: url('data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzE4NDIwODE0MjM1IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjYgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ0NzgiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwLjM5MDYyNSIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik00OTAuMiA0NDUuMmMwLTE4IDEyLjYtMzIuOSAyOC0zMi45IDE1LjUgMCAyOCAxMy4xIDI4IDMxLjFsMCAxNzguOGMwIDE4LTEyLjYgMzAuMi0yOCAzMC4yLTE1LjUgMC0yOC0xMi0yOC0yOS45TDQ5MC4yIDQ0NS4yek01MTguNyA3NDcuNGMtMTcuMSAwLTMwLjktMTQuMS0zMC45LTMxLjYgMC0xNy40IDEzLjgtMzEuNiAzMC45LTMxLjYgMTcuMSAwIDMwLjkgMTQuMSAzMC45IDMxLjZDNTQ5LjYgNzMzLjMgNTM1LjggNzQ3LjQgNTE4LjcgNzQ3LjRMNTE4LjcgNzQ3LjR6IiBwLWlkPSI0NDc5Ij48L3BhdGg+PHBhdGggZD0iTTU2OC44MzIgMTk2LjYwOGMyMC45OTIgMzUuMzI4IDMzMy4zMTIgNTYwLjY0IDM0OS4xODQgNTg3Ljc3NiAxNC4zMzYgMjQuMDY0IDE0LjMzNiA0Ny4xMDQgNi42NTYgNjEuOTUyLTguNzA0IDE2LjM4NC0yOS42OTYgMjEuNTA0LTUyLjczNiAyMS41MDQtMTguOTQ0IDAtNjc4LjkxMiAwLTcwOS42MzIgMC0yOC4xNiAwLTQ5LjE1Mi03LjY4LTU1LjI5Ni0xOS45NjgtNy42OC0xNi4zODQtNi4xNDQtMzcuODg4IDExLjI2NC02Ny4wNzJDMTM3LjIxNiA3NDkuMDU2IDQ0Ny40ODggMjE2LjA2NCA0NTcuNzI4IDE5OS42OGMxNS4zNi0yNi4xMTIgMzcuMzc2LTQzLjUyIDU3Ljg1Ni00My41MkM1MzYuMDY0IDE1Ni4xNiA1NTYuNTQ0IDE3Ni42NCA1NjguODMyIDE5Ni42MDh6TTUwMi4yNzIgMjUyLjQxNmMtMTEuNzc2IDE5Ljk2OC0zMDIuMDggNTEzLjAyNC0zMTYuOTI4IDUzNy4wODgtNi42NTYgMTEuMjY0LTguMTkyIDE3LjkyIDYuNjU2IDE3LjkyIDQ0LjU0NCAwIDYwMi42MjQgMCA2NDUuNjMyIDAgMTQuMzM2IDAgMTUuODcyLTguMTkyIDkuNzI4LTE4LjQzMi0yNS4wODgtNDEuNDcyLTMwOC43MzYtNTE4LjE0NC0zMjEuMDI0LTUzNy42QzUxNC41NiAyMzEuOTM2IDUxNC4wNDggMjMyLjQ0OCA1MDIuMjcyIDI1Mi40MTZ6IiBwLWlkPSI0NDgwIj48L3BhdGg+PC9zdmc+'); /* 替换为你的图片路径 */
      background-size: cover;
      background-position: center;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    #${chatWindowId} .alert-text p{
      margin: 0 auto;
    }
`;

// 将style元素添加到head中
document.head.appendChild(style);

class ChatRobot {

  /**
   *
   * @param appId 平台提供的appId
   * @param userId 接入方用户id
   * @param username 接入方用户名
   * @param orgName 接入方组织名称
   * @param sign 签名，当token有值时，优先使用token
   * @param orgCode 接入方组织编码
   * @param token token，平台方内部使用，当token有值时，优先使用token
   * @param businessCode 业务编码
   * @param mode 模式 1: 根目录下创建 2: 指定父元素创建
   * @param parentId 父元素id
   * @param onSignError 签名异常回调函数
   */
  constructor({
                appId,
                userId,
                username,
                orgName,
                sign,
                orgCode,
                token,
                businessCode = 'AI_Chat',
                mode = 1,
                parentId = '',
                url,
                onSignError,
                top=0,
                showEchart,
                areaCode,
                roleCode
              }) {
    this.token = token
    this.iframe = null
    this.chatWindow = null
    this.baseFontSize = 16
    this.mode = mode
    this.parentId = parentId
    this.businessCode = businessCode
    this.initWidth = null
    this.appId = appId
    this.userId = userId
    this.username = username
    this.orgName = orgName
    this.sign = sign
    this.orgCode = orgCode
    this.onSignError = onSignError
    this.jumpKey = null
    this.iframeExist = false
    this.url = url
    this.top = top
    this.showEchart = showEchart
    this.areaCode = areaCode
    this.roleCode = roleCode
  }

  convert(px) {
    return px / this.baseFontSize
  }

  /**
   * 显示聊天窗口
   */
  show() {
    if (!this.token) {
      if (!this.sign) {
        alert('参数sign不能为空')
        return
      }
      if (!this.appId) {
        alert('参数appId不能为空')
        return
      }
      if (!this.userId) {
        alert('参数userId不能为空')
        return
      }
      this.url = import.meta.env.VITE_CHAT_ROBOT_AUTH_URL
    }


    if (this.mode == 2 && !this.parentId) {
      alert('请指定内嵌式聊天窗口的父元素id')
      return
    }


    let chatWindow = this.mode == 2 ? window.document.querySelector(`#${this.parentId} #${chatWindowId}`) : window.document.querySelector(`#${chatWindowId}`)
    if (chatWindow) {
      chatWindow.style.display = null
    } else {
      chatWindow = document.createElement('div')
      this.chatWindow = chatWindow
      chatWindow.id = `${chatWindowId}`
      if (this.mode == 2) {
        let width = document.querySelector(`#${this.parentId}`).offsetWidth;
        let height = document.querySelector(`#${this.parentId}`).offsetHeight;
        this.initWidth = width
        // chatWindow.style.cssText = `
        //           margin: 0;
        //           border: 0;
        //           padding: 0;
        //           right: 0;
        //           width: ${this.convert(width)}rem;
        //           height: ${this.convert(height)}rem;
        //           background: rgba(255, 255, 255);
        //           box-sizing: border-box;
        //           z-index: 80;
        //           pointer-events: auto;
        //           position: fixed;
        //         `
        chatWindow.style.cssText = `
                  margin: 0;
                  border: 0;
                  padding: 0;
                  width: ${this.convert(width)}rem;
                  height: calc(100vh - ${this.top}px);
                  background: rgba(255, 255, 255);
                  box-sizing: border-box;
                  z-index: 80;
                  pointer-events: auto;
                  position: fixed;
                  border-radius: 4px;
                `
        document.querySelector(`#${this.parentId}`).appendChild(chatWindow)
      } else {
        this.initWidth = 360
        chatWindow.style.cssText = `
                  position: fixed;
                  right: 0;
                  bottom: 0;
                  width: ${this.convert(360)}rem;
                  height: 100vh;
                  border: ${this.convert(1)}rem solid #ffffff;
                  border-radius: ${this.convert(24)}rem 0 0 ${this.convert(24)}rem;
                  background: rgba(255, 255, 255, 0.85);
                  box-sizing: border-box;
                  backdrop-filter: blur(${this.convert(10)}rem);
                  box-shadow: 0 0 ${this.convert(32)}rem 0 rgba(0, 0, 0, 0.3);
                  z-index: 80;
                  pointer-events: auto;
                  margin: 0;
                  padding: 0;
                `
        document.body.appendChild(chatWindow)
      }

      chatWindow.innerHTML = `
                <div class="alert-container">
                  <div class="alert-text">
                    <div class="alert-image"></div>
                    <p>异常</p>
                  </div>
                </div>
                <div class="chat_robot_loading">
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                </div>
            `
      let zheZhao = document.createElement('div')
      chatWindow.appendChild(zheZhao)
      zheZhao.style.cssText = `
                background: transparent;
                width: 100%;
                height: 100%;
                position: absolute;
                z-index: 100;
                display: none;
            `

      if (this.mode == 1) {
        let tuoZhuaiImg = document.createElement('img')
        tuoZhuaiImg.src = tuoZhuaiImgDefault
        tuoZhuaiImg.style.cssText = `
              position: absolute;
              top: 50%;
              cursor: w-resize;
              width: ${this.convert(10)}rem;
              height: ${this.convert(20)}rem;
              z-index: 999;
              display: inline;
              vertical-align: baseline;
              box-sizing: content-box;
              left: 4px;
            `

        tuoZhuaiImg.addEventListener('dblclick', () => {
          if (chatWindow.style.width == `${this.convert(1320)}rem`) {
            chatWindow.style.width = `${this.convert(360)}rem`
          } else {
            chatWindow.style.width = `${this.convert(1320)}rem`
          }
          // iframe.style.zIndex = 998
          // chatWindow.style.zIndex = -998
          tuoZhuaiImg.src = tuoZhuaiImgDefault;
        });

        tuoZhuaiImg.addEventListener('mouseover', function (event) {
          tuoZhuaiImg.src = tuoZhuaiImgSelect;
        })

        tuoZhuaiImg.addEventListener('mouseout', function (event) {
          tuoZhuaiImg.src = tuoZhuaiImgDefault;
        })


        chatWindow.appendChild(tuoZhuaiImg)

        interact(`#${chatWindowId}`).resizable({
          edges: {left: true, right: false, bottom: false, top: false},
          listeners: {
            move: (event) => {
              const target = event.target
              let x = parseFloat(target.getAttribute('data-x')) || 0
              target.style.width = `${this.convert(event.rect.width)}rem`
              x += event.deltaRect.left
              target.setAttribute('data-x', x)
            }
          },
          modifiers: [
            interact.modifiers.restrictEdges({outer: 'parent'}),
            interact.modifiers.restrictSize({
              min: {width: 360},
              max: {width: 1320}
            })
          ],
          inertia: true
        })

        chatWindow.addEventListener('mousedown', function (event) {
          zheZhao.style.display = 'block'
          tuoZhuaiImg.src = tuoZhuaiImgSelect;
        })

        chatWindow.addEventListener('mouseup', function (event) {
          zheZhao.style.display = 'none'
          tuoZhuaiImg.src = tuoZhuaiImgDefault;
        })

        document.querySelector('body').addEventListener('mouseover', function (event) {
          if (event.buttons) {
            zheZhao.style.display = 'block'
          } else {
            zheZhao.style.display = 'none'
          }
        })
      }

      let showEchart = false;

      window.addEventListener('message', e => {
        if (e.data.command == 'closeChatRobot') {
          this.hide()
        } else if (e.data.command == 'maxWidthChatRobot') {
          showEchart = true
          chatWindow.style.width = `${this.convert(1320)}rem`
        } else if (e.data.command == 'returnLastWidthChatRobot') {
          showEchart = false
          // chatWindow.style.width = `${this.convert(this.initWidth)}rem`
          // console.log(document.querySelector(`#${this.parentId} `).offsetWidth)
          chatWindow.style.width = document.querySelector(`#${this.parentId} `).offsetWidth + 'px'
        } else if (e.data.command == 'authenticateError') {
          this.iframe.style.display = 'none'
          document.querySelector(`#${chatWindowId} .chat_robot_loading`).style.display = 'none'
          document.querySelector(`#${chatWindowId} .alert-container`).style.display = 'block'
          document.querySelector(`#${chatWindowId} .alert-container p`).textContent = e.data.errMsg
          // this.onSignError && this.onSignError()
        } else if (e.data.command == 'removeLoading') {
          document.querySelector(`#${chatWindowId} .chat_robot_loading`).style.display = 'none'
          document.querySelector(`#${chatWindowId} .alert-container`).style.display = 'none'
          this.iframe.style.display = 'flow'
        } else if (e.data.command == 'showEchart') {
          let echartConfig = JSON.parse(e.data.echartConfig)
          this.showEchart && this.showEchart(echartConfig)
        }
      }, false)

      if(this.mode == 2){
        let _this = this
        window.addEventListener('resize', function(event) {
          if(!showEchart){
            console.log('change')
            let width = document.querySelector(`#${_this.parentId}`).offsetWidth;
            // let height = document.querySelector(`#${_this.parentId}`).offsetHeight;
            chatWindow.style.width = width + 'px'
            // chatWindow.style.height = height + 'px'
          }
        });
      }

      let iframe = document.createElement('iframe')
      this.iframe = iframe
      iframe.allow = 'microphone'
      iframe.style.cssText = `
              width: 100%;
              height: 100%;
              border: none;
              z-index: 90;
              position: relative;
              display: inline;
              vertical-align: baseline;
              box-sizing: content-box;
              margin: 0;
              padding: 0;
              // border-radius: 1.5rem 0px 0px 1.5rem;
              border-radius: 24px 0px 0px 24px;
            `
      if (iframe.attachEvent) {
        iframe.attachEvent('onload', () => {
          this.iframeRefreshConfig(this.mode, this.token, this.businessCode, this.appId,
              this.userId, this.username, this.orgName, this.sign, this.orgCode, this.jumpKey, this.areaCode, this.roleCode)
        });
      } else {
        iframe.onload = () => {
          this.iframeRefreshConfig(this.mode, this.token, this.businessCode, this.appId,
              this.userId, this.username, this.orgName, this.sign, this.orgCode, this.jumpKey, this.areaCode, this.roleCode)
        };
      }
      if(this.token){
        iframe.src = this.url
        chatWindow.appendChild(iframe)
      }else {
        this.getIframeTargetUrl()
      }
    }
  }

  getIframeTargetUrl() {
    let xhr = new XMLHttpRequest();
    xhr.open('GET', this.url + '?appId=' + this.appId);
    xhr.setRequestHeader('RobotSign', this.sign)
    xhr.setRequestHeader('RobotUserId', this.userId)
    xhr.onload = () => {
      if (xhr.readyState === xhr.DONE && xhr.status === 200) {
        let jumpUrl = JSON.parse(xhr.response).data.jumpUrl
        this.jumpKey = jumpUrl.split('?jumpKey=')[1]
        this.iframe.src = encodeURI(jumpUrl);
        if(!this.iframeExist){
          this.chatWindow.appendChild(this.iframe)
        }
      } else {
        document.querySelector(`#${chatWindowId} .chat_robot_loading`).style.display = 'none'
        document.querySelector(`#${chatWindowId} .alert-container`).style.display = 'block'
        document.querySelector(`#${chatWindowId} .alert-container p`).textContent = JSON.parse(xhr.response).errMsg
        let errCode = JSON.parse(xhr.response).errCode
        if(4002 == errCode || 4003 == errCode || 4004 == errCode || 4005 == errCode || 4006 == errCode || 4007 == errCode){
          this.onSignError && this.onSignError()
        }
      }

    };
    xhr.send();
  }

  /**
   * 隐藏聊天窗口
   */
  hide() {
    if (this.mode == 2) {
      return
    }
    window.document.querySelector(`#${chatWindowId}`).style.display = 'none'
  }

  /**
   * 重新配置
   * @param sign
   */
  reconfigure({sign}) {
    if (!this.sign) {
      alert('参数sign不能为空')
      return
    }
    document.querySelector(`#${chatWindowId} .chat_robot_loading`).style.display = 'flex'
    document.querySelector(`#${chatWindowId} .alert-container`).style.display = 'none'
    this.sign = sign
    this.refreshConfig(this.mode, this.token, this.businessCode, this.appId,
        this.userId, this.username, this.orgName, this.sign, this.orgCode, this.jumpKey, this.areaCode, this.roleCode)
  }

  /**
   * 刷新token
   * @param {*} token
   */
  refreshToken(token) {
    this.iframe.contentWindow.postMessage({
      'token': token,
      'command': 'refreshToken'
    }, '*');
  }


  /**
   * 刷新config
   */
  refreshConfig(mode, token, businessCode, appId, userId, username, orgName, sign, orgCode, jumpKey, areaCode, roleCode) {
    if (this.iframe.contentWindow) {
        this.iframe.contentWindow.postMessage({
          'mode': mode,
          'businessCode': businessCode,
          'token': token,
          'appId': appId,
          'userId': userId,
          'username': username,
          'orgName': orgName,
          'orgCode': orgCode,
          'sign': sign,
          'jumpKey': jumpKey,
          'areaCode': areaCode,
          'roleCode': roleCode,
          'command': 'refreshConfig'
        }, '*');
    } else {
      this.getIframeTargetUrl()
    }
  }
  iframeRefreshConfig(mode, token, businessCode, appId, userId, username, orgName, sign, orgCode, jumpKey, areaCode, roleCode) {
    window.addEventListener('message', e => {
      if (e.data.command == 'initComponentFinish') {
          if (this.iframe.contentWindow) {
              this.iframe.contentWindow.postMessage({
                'mode': mode,
                'businessCode': businessCode,
                'token': token,
                'appId': appId,
                'userId': userId,
                'username': username,
                'orgName': orgName,
                'orgCode': orgCode,
                'sign': sign,
                'jumpKey': jumpKey,
                'areaCode': areaCode,
                'roleCode': roleCode,
                'command': 'refreshConfig'
              }, '*');
          } else {
            this.getIframeTargetUrl()
          }
        }
      }, false)
  }
}


window.ChatRobot = ChatRobot
