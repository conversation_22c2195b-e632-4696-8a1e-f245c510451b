<template>
  <div class="chat-item" :class="[timeType == 'topping' ? 'chat-item-top' : '']" @mouseleave="hideButtonBox()" @click="itemClick">
    <div class="chat-item-title-edit-container" v-if="isEditMode">
      <div class="chat-item-title-edit-container-input">
        <input ref="inputRef" v-model="inputValue" placeholder="请输入..." @blur="handleBlur" @click="handleClick" maxlength="256">
      </div>
      <div class="confirm-button" @mouseenter="buttonEnter('confirmButton')" @mouseleave="buttonLeave('confirmButton')"
           @click.stop="saveNewName">
        <SvgIcon
          :icon="confirmButtonIcon"
          class="!h-[20px] !w-[20px]"
        />
      </div>
      <div class="cancel-button" @mouseenter="buttonEnter('cancelButton')" @mouseleave="buttonLeave('cancelButton')"
           @click.stop="cancelRename">
        <SvgIcon
          :icon="cancelButtonIcon"
          class="!h-[20px] !w-[20px]"
        />
      </div>
    </div>
    <div class="chat-item-title-container" v-else>
      <div class="title" v-html="htmlTitle || inputValue"></div>
      <div class="button" @mouseleave="buttonBoxMouseLeave">
        <div class="time">{{timeFormat}}</div>
        <Button type="text" size="small" class="!h-[20px] !w-[20px] !p-0 !border-0" @click="showButtonBox">
          <template #icon>
            <SvgIcon v-if="isShowButtonBox"
              icon="icon-history-menu-active"
              class="!h-[20px] !w-[20px]"
            />
            <SvgIcon v-else
              icon="icon-history-menu"
              class="!h-[20px] !w-[20px]"
            />
          </template>
        </Button>
        <div ref="buttonBoxRef" class="button-box" :class="[isShowButtonBox ? '' : 'invisible']"
             @mouseleave="isShowButtonBox = false"
             @mouseenter="clearButtonBoxMouseLeaveTimer"
        >
          <div class="button-box-item" @click="renameChat($event)">
            <div class="button-box-item-icon">
              <SvgIcon
                icon="icon-history-chat-rename"
                class="!h-[16px] !w-[16px] !pt-[1px]"
              />
            </div>
            <div class="button-box-item-name">
              重命名
            </div>
          </div>
          <div class="button-box-item" @click="topChat($event)">
            <div class="button-box-item-icon">
              <SvgIcon
                :icon="timeType == 'topping' ? 'icon-history-menu-top-cancel' : 'icon-history-menu-top'"
                class="!h-[16px] !w-[16px] !pt-[1px]"
              />
            </div>
            <div class="button-box-item-name">
              {{timeType == 'topping' ? '取消置顶' : '置顶'}}
            </div>
          </div>
<!--          <div class="button-box-item" @click="shareChat($event)">-->
<!--            <div class="button-box-item-icon">-->
<!--              <SvgIcon-->
<!--                icon="icon-history-chat-share"-->
<!--                class="!h-[16px] !w-[16px] !pt-[1px]"-->
<!--              />-->
<!--            </div>-->
<!--            <div class="button-box-item-name">-->
<!--              分享-->
<!--            </div>-->
<!--          </div>-->
          <div class="button-box-item" @click="deleteChat($event)">
            <div class="button-box-item-icon">
              <SvgIcon
                icon="icon-history-menu-delete"
                class="!h-[16px] !w-[16px] !pt-[1px]"
                color="var(--error-6)"
              />
            </div>
            <div class="button-box-item-name button-box-item-name-warn">
              删除
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-modal v-model:open="open" title="删除对话" :closable="false" :width="400" :get-container="getContainer">
      <p>删除后无法恢复，是否继续删除？</p>
      <template #footer>
        <a-button type="text" size="small" @click="cancel">取消</a-button>
        <a-button type="primary" size="small" danger @click="ok">删除</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {Button, message} from 'ant-design-vue'
import {SvgIcon} from "@/components/";
import {ref, nextTick, computed} from 'vue'
import emitter from '@/utils/mitt'
import {updateChatTitle, deleteChatHistory, topChatHistory, cancelTopChatHistory} from '@/api/history'
import {useChatHistoryStore} from '@/store'
import moment from 'moment'
const chatHistoryStore = useChatHistoryStore()

const props = defineProps({
  title: {
    default: '',
    type: String
  },
  htmlTitle: {
    default: '',
    type: String
  },
  chatId: {
    default: '',
    type: String
  },
  timeType: {
    default: '',
    type: String
  },
  lastActiveTime: {
    default: null,
    type: String
  }
})

const open = ref(false)
const inputRef = ref(null)
const inputValue = ref(props.title)
const buttonBoxRef = ref(null)
const isEditMode = ref(false)
const blurStopSign = ref(false)
const isShowButtonBox = ref(false)
const topValue = ref('25px')
const confirmButtonIcon = ref('icon-history-rename-confirm')
const cancelButtonIcon = ref('icon-history-rename-cancel')

const timeFormat = computed(() => {
  if(props.lastActiveTime){
    return moment(props.lastActiveTime).format('YYYY年M月D日')
  }
  return ''
})

function getContainer(){
  return document.querySelector('.chat-item') as HTMLElement
}

function cancel() {
  open.value = false
}

function ok() {
  const chatId = props.chatId
  deleteChatHistory({ chatId: chatId })
    .then(() => {
      message.success('删除成功')
      chatHistoryStore.deleteChat(props.timeType, chatId)
      emitter.emit('reLoadData')
    })
    .catch((e) => {
      message.error('删除失败')
    })
    .finally(() => {
      open.value = false
      hideButtonBox()
    })
}

function itemClick() {
  emitter.emit('chatItemClick', props.chatId);
}

function showButtonBox(e) {
  e.stopPropagation()
  if (e.clientY + buttonBoxRef._value.clientHeight + 15 > document.querySelector('body').offsetHeight) {
    topValue.value = `-${buttonBoxRef._value.clientHeight}px`
  } else {
    topValue.value = '22px'
  }
  isShowButtonBox.value = true
}

function hideButtonBox() {
  isShowButtonBox.value = false
}

function buttonLeave(buttonType) {
  blurStopSign.value = false
  if (buttonType === 'confirmButton') {
    confirmButtonIcon.value = 'icon-history-rename-confirm'
  } else if (buttonType === 'cancelButton') {
    cancelButtonIcon.value = 'icon-history-rename-cancel'
  }
}

function buttonEnter(buttonType) {
  blurStopSign.value = true
  if (buttonType === 'confirmButton') {
    confirmButtonIcon.value = 'icon-history-rename-confirm-hover'
  } else if (buttonType === 'cancelButton') {
    cancelButtonIcon.value = 'icon-history-rename-cancel-hover'
  }
}

function saveNewName() {
  let oldTitle = props.title
  isEditMode.value = false
  blurStopSign.value = false
  updateChatTitle({
    title: inputValue.value,
    chatId: props.chatId
  }).then((res) => {
    message.success('完成重命名')
    chatHistoryStore.updateChatTitle(props.timeType, props.chatId, inputValue.value)
    emitter.emit('reLoadData');
  }).catch((e) => {
    message.error('重命名失败')
    inputValue.value = oldTitle
  })
}

function cancelRename() {
  inputValue.value = props.title
  isEditMode.value = false
  blurStopSign.value = false
  buttonLeave('cancelButton')
}

function renameChat(e) {
  e.stopPropagation()
  isEditMode.value = true
  hideButtonBox()
  nextTick(() => {
    inputRef.value.focus()
  })
}

function topChat(e) {
  e.stopPropagation()
  if (props.timeType === 'topping') {
    cancelTopChatHistory({
      chatId: props.chatId
    }).then(() => {
      message.success('已取消置顶对话')
      chatHistoryStore.updateChatTopStatus(props.timeType, props.chatId, false)
      emitter.emit('reLoadData');
    }).catch((e) => {
      message.error('取消对话置顶失败')
    }).finally(() => {
      hideButtonBox()
    })
  }else {
    topChatHistory({
      chatId: props.chatId
    }).then(() => {
      message.success('已置顶对话')
      chatHistoryStore.updateChatTopStatus(props.timeType, props.chatId, true)
      emitter.emit('reLoadData');
    }).catch((e) => {
      message.error('对话置顶失败')
    }).finally(() => {
      hideButtonBox()
    })
  }
}

function shareChat(e) {
  e.stopPropagation()
  hideButtonBox()
}

function deleteChat(e) {
  e.stopPropagation()
  open.value = true
  // deleteChatHistory({
  //   chatId: props.chatId
  // }).then(() => {
  //   message.success('对话删除成功')
  //   chatHistoryStore.deleteChat(props.timeType, props.chatId)
  //   emitter.emit('reLoadData');
  // }).catch((e) => {
  //   message.error('对话删除失败')
  // }).finally(() => {
  //   hideButtonBox()
  // })
}

function handleBlur() {
  if (blurStopSign.value) {
    return
  }
  isEditMode.value = false
  inputValue.value = props.title
}

function handleClick(e) {
  e.stopPropagation() // blur和click事件冲突，click事件会触发input的blur事件，所以通过阻止事件冒泡来避免冲突
}

let buttonBoxMouseLeaveTimer = null

function buttonBoxMouseLeave(){
  clearButtonBoxMouseLeaveTimer()
  buttonBoxMouseLeaveTimer = setTimeout(() => {
    isShowButtonBox.value = false
  }, 100)
}

function clearButtonBoxMouseLeaveTimer(){
  if(buttonBoxMouseLeaveTimer){
    clearTimeout(buttonBoxMouseLeaveTimer)
  }
}
</script>

<style lang="scss"  scoped>
.chat-item {
  width: 100%;
  border-radius: var(--border-radius-8);
  display: flex;
  flex-direction: column;
  padding: 12px;
  gap: 8px;
  cursor: pointer;
  height: 52px;
  justify-content: center;
  border: 1px solid var(--line-1);
  background-color: var(--fill-0);

  .chat-item-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;

    .button {
      height: 20px;
      position: relative;
      width: fit-content;
      flex: none;

      .time {
        display: inline;
        font-family: Source Han Sans;
        font-size: var(--font-12);
        font-weight: normal;
        line-height: normal;
        text-align: right;
        letter-spacing: 0em;
        font-feature-settings: "kern" on;
        color: var(--text-2);
      }

      button {
        display: none;
      }

      .button-box {
        position: absolute;
        width: 128px;
        display: flex;
        flex-direction: column;
        padding: 4px;
        background: var(--fill-0);
        box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.15);
        right: 0;
        top: v-bind(topValue);
        border-radius: var(--border-radius-8);
        z-index: 999;

        .button-box-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding: 8px 0 8px 12px;
          gap: 12px;
          cursor: pointer;

          .button-box-item-name {
            font-family: Source Han Sans;
            font-size: var(--font-14);
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0em;
            color: var(--text-5);
          }

          .button-box-item-name-warn {
            color: var(--error-6);
          }
        }

        .button-box-item:hover {
          background: var(--fill-2);
          border-radius: var(--border-radius-4);
        }
      }

      .ant-btn-text:not(:disabled):hover {
        background-color: transparent !important;
      }

      .ant-btn:not(:disabled):focus-visible {
        outline: none !important;
      }

      .ant-btn-text:not(:disabled):active {
        border-radius: var(--border-radius-10) !important;
        opacity: 1 !important;
        background: var(--fill-3) !important;
      }
    }

    .title {
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: bold;
      line-height: normal;
      text-align: justify;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      color: var(--text-5);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 20px;

      .highlight {
        color: var(--main-4);
      }

    }

  }

  .chat-item-title-edit-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;

    .chat-item-title-edit-container-input {
      width: 100%;

      input {
        font-family: Source Han Sans;
        font-size: var(--font-14);
        font-weight: bold;
        line-height: normal;
        text-align: justify; /* 浏览器可能不支持 */
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        /* 中性色/中性色-11 */
        color: var(--text-5);
        background: transparent;
        width: 100%
      }
    }

    .confirm-button, .cancel-button {
      cursor: pointer;
    }
  }

  .chat-item-content {
    font-family: Source Han Sans;
    font-size: var(--font-14);
    font-weight: normal;
    line-height: normal;
    text-align: justify;
    letter-spacing: 0em;
    color: var(--fill-6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 20px;
  }
}

.chat-item:hover {
  background: var(--fill-1);

  .button button {
    display: inline;
  }

  .button .time {
    display: none;
  }
}

.chat-item-top {
  background-color: var(--fill-1);
}
</style>
