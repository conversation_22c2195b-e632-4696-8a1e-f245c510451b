<template>
  <div class="case-item" :class="{'has-sort': data.sortId}">
    <SortNumber v-if="data.sortId" :content="data.sortId"/>
    <div class="title-box">
      <span class="title">{{ data.title }}</span>
      <span  v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span>
    </div>
    <a-typography-paragraph class="content" :content="data.content" :ellipsis="{ rows: 5 }"/>

    <raw-website :data="website"/>
  </div>
</template>
<script setup lang="ts">
import RawWebsite from '@/views/chat/components/quote/raw-website.vue';
import {computed} from 'vue'
import {Website} from '@/typings/chat';
import SortNumber from '@/views/chat/components/quote/sort-number.vue'
type Props = {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})

const website = computed<Website>(() => {
  return {
    name: props.data.siteName,
    url: props.data.url
  }
})
</script>
<style scoped lang="scss">
.case-item {
  position: relative;
  width: 100%;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-radius: var(--border-radius-12);
  background: rgba(var(--fill-rbg-7),0.5);
  padding: 16px;
  &.has-sort{
    padding:30px 16px 16px 16px;
  }
  .title-box {
    display: flex;
  }
  .title {
    font-family: 思源黑体;
    font-weight: bold;
    line-height: normal;
    color: var(--text-4);
  }
  .content {
    font-family: Source Han Sans;
    line-height: 22px;
    color: var(--text-4);
    white-space: break-spaces;
  }
}
</style>
