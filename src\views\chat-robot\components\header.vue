<template>
  <div class="chat-robot-header">
    <div class="title">智能问答</div>
    <SvgIcon icon="icon-close" class="w-[20px] h-[20px] close" @click="close" />
  </div>
</template>

<script setup lang='ts'>
import { SvgIcon } from '@/components'

defineOptions({
  name: 'ChatRobotHeader'
})
const close = ()=>{
  window.parent.postMessage({
    'command': 'closeChatRobot'
  }, '*')
}
</script>

<style lang="scss" scoped>
.chat-robot-header {
  width: 100%;
  display: flex;
  align-items: center;

  .title {
    flex-grow: 1;
    height: 26px;
    font-family: 思源黑体;
    font-size: var(--font-18);
    font-weight: bold;
    line-height: normal;
  }

  .close {
    cursor: pointer;
  }
}
</style>
