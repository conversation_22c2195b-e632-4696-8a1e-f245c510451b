<template>
  <div class="w-full center-wrap">
    <div class="center-qa-container">
      <div class="center-qa center-index">
          <!-- 默认 -->
          <div v-if="state.isNewChat" class="w-full title">
            <img :src="loadThemeImg('logo.png')"  alt="logo" width="40" height="40"/>
            <div class="t1 system-title">{{ systemInfo.title }}</div>
            <div class="t1 t2">有什么我能帮到您？</div>
          </div>
          <template v-else>
            <!-- 非新建对话 -->
            <div id="QaRef" ref="qaRef" class="qa qa-container">
              <qa-item
              v-for="(item,index) in state.qaList"
              :key="index"
              :item="item"
              :index="index"
              @open-quote="openQuote"
              @open-quote-search="openQuoteSearch"/>
            </div>
          </template>
          <!-- 输入框 有且只有一个对话并且是敏感时，无输入框-->
          <chat-input v-if="showInput"/>
          <!-- 默认 -->
          <question-default v-if="state.isNewChat" />
      </div>
    </div>
    <!-- 引用依据 -->
    <quote-index v-show="state.isShowQuote" ref="quoteRef"/>
    <!-- 检索 -->
    <quote-search v-show="state.isShowQuoteSearch" ref="quoteSearchRef"/>
    <!-- 正文 -->
    <file-reader v-if="state.isShowOriginal" :is-new-target="false" />
  </div>
</template>

<script setup lang='ts'>
import { computed, ref, nextTick } from 'vue'
import QuestionDefault from '@/views/chat/components/question-default.vue'
import ChatInput from '@/views/chat/components/center/chat-input.vue'
import QaItem from '@/views/chat/components/center/qa-item.vue'
import QuoteIndex from '@/views/chat/components/quote/index.vue'
import QuoteSearch from '@/views/chat/components/quote/index-search.vue'
import FileReader from '@/views/file-reader/index.vue'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { useMenusStore } from '@/store'
import { loadThemeImg } from '@/hooks/use-theme'
// 系统信息
const menusStore = useMenusStore()
const systemInfo = computed(() => menusStore.systemInfo)
const { state } = useChatGptInject()
// <!-- 输入框 有且只有一个对话并且是敏感时，无输入框-->
const showInput = computed(()=> !(state.qaList.length === 1 && state.qaList[0].sensitive))
// 打开引用
const quoteRef = ref()
const openQuote = (val: string)=> {
  state.isShowQuote = true
  state.isShowOriginal = false
  state.isShowQuoteSearch = false
  state.selectTag = ''
  nextTick(()=>{
    quoteRef.value?.loadQuote(val)
  })
}
// 打开检索
const quoteSearchRef = ref()
const openQuoteSearch = (val: string)=> {
  state.isShowQuote = false
  state.isShowOriginal = false
  state.isShowQuoteSearch = true
  nextTick(()=>{
    quoteSearchRef.value?.loadQuoteSearch(val)
  })
}
</script>

<style lang="scss" scoped>
.center-wrap {
  display: flex;
  justify-content: center;
  flex-grow: 1;
  gap: 24px;
  height: calc(100% - 84px);
  .center-qa-container {
    flex:1;
    min-width: 450px;
    display: flex;
    justify-content: center;
  }
}
.center-index {
  position: relative;
  width: 100%;
  display: flex;
  max-width: 872px;
  flex-direction: column;
  justify-content: center;
  .title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    .t1 {
      font-family: 思源黑体;
      font-weight: bold;
      line-height: normal;
      color: var(--main-6);
    }

    .t2 {
      padding: 8px 0 0 0;
      font-family: Source Han Sans;
      font-size: var(--font-24);
      color: var(--text-5);
    }
  }

  .qa {
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 32px;
    flex-grow: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    box-sizing: border-box;
    padding-bottom: 16px;
    background:var(--logo-bg) no-repeat center center;
  }
}

</style>
