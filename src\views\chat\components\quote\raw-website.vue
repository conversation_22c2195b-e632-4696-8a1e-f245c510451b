<!-- 站点信息 -->
<template>
  <div v-if="data.url" class="raw-website" @click="go">
    <div>
      <SvgIcon class="w-[24px] h-[24px]" icon="icon-link"/>
    </div>
    <div class="detail">
      <a-tooltip>
        <template #title>点击跳转</template>
        <div v-if="data.name">{{ data.name }}</div>
        <a-typography-text class="url" :content="data.url" ellipsis/>
      </a-tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import {SvgIcon} from "@/components";
import {Website} from "@/typings/chat";

type Props = {
  data: Website
}

const props = defineProps<Props>()

function go() {
  window.open(props.data.url, '_blank')
}
</script>
<style scoped lang="scss">
.raw-website {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--fill-0);
  border-radius: var(--border-radius-8);
  padding: 8px;

  .detail {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    font-size: var(--font-12);
    width: calc(100% - 24px);

    .url {
      font-family: Source Han Sans;
      color: var(--fill-6);
      font-size: var(--font-12);
    }
  }
}
</style>
