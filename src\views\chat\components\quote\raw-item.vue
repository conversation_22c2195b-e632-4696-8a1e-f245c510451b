<template>
  <div class="raw-item"  :class="{'has-sort': data.sortId}">
    <SortNumber v-if="data.sortId" :content="data.sortId"/>
    <div class="title-box">
      <div class="title">
        <span class="s1">{{ data.fileName }}</span>
        <span v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span>
      </div>
      <a-button  class="btn" type="primary" size="small" @click="readFile(data)">阅读原文</a-button>
    </div>

    <div v-for="(item,index) in data.list" :key="index" class="d2">
      <span class="s1">{{ item.article }}</span>
      <span class="s2">{{ item.text }}</span>
    </div>

    <raw-website v-if="data.fileType === 1" :data="website"/>

    <div v-if="data.fileType === 2" class="file">
      <SvgIcon v-if="data.suffixType === 1" class="w-[24px] h-[24px]" icon="icon-word"/>
      <SvgIcon v-if="data.suffixType === 2" class="w-[24px] h-[24px]" icon="icon-ppt"/>
      <SvgIcon v-else class="w-[24px] h-[24px]" icon="icon-file"/>
      <span class="filename">{{ data.fileName }}</span>

      <a-tooltip>
        <template #title>点击下载</template>
        <a-button v-if="!isVisitor" type="link" size="small" @click="download">
          <template #icon>
            <div>
              <DownloadOutlined/>
            </div>
          </template>
        </a-button>
      </a-tooltip>
    </div>

  </div>
</template>
<script setup lang="ts">
import {computed} from 'vue'
import {Website} from '@/typings/chat'
import {SvgIcon} from '@/components'
import {DownloadOutlined} from '@ant-design/icons-vue'
import {apiDownRefs} from '@/api/download'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import {  getCookie } from '@/utils/app-gateway'
import RawWebsite from '@/views/chat/components/quote/raw-website.vue'
import SortNumber from '@/views/chat/components/quote/sort-number.vue' 
type Props = {
  chatId: string
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})
const bosssoftCookie = getCookie()
const isVisitor = computed(() => bosssoftCookie?.isVisitor)
const { readFile } = useChatGptInject()

const website = computed<Website>(() => {
  return {
    name: props.data.siteName,
    url: props.data.url
  }
})

function download() {
  let fileId = props.data.fileId
  apiDownRefs(fileId)
}

</script>
<style scoped lang="scss">
.raw-item {
  position: relative;
  width: 100%;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-radius: var(--border-radius-12);
  background: rgba(var(--fill-rbg-7),0.5);
  padding: 16px;
  &.has-sort{
    padding:30px 16px 16px 16px;
  }
  .title-box {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .title {
    display: flex;
    align-items: center;
    font-family: 思源黑体;
    font-weight: bold;
    line-height: normal;
    color: var(--text-4);
    flex-grow: 1;
  }
  .btn {
    margin-left: 8px;
  }

  .d2 {
    font-family: Source Han Sans;
    color: var(--text-4);
    white-space: break-spaces;

    .s1 {
      font-weight: bold;
    }

    .s2 {
      padding-left: 8px;
    }
  }

  .file {
    padding: 8px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    background: var(--fill-0);
    border-radius: var(--border-radius-8);
    .svg-icon,
    .ant-btn {
      flex-shrink: 0;
    }
    .filename {
      flex-grow: 1;
      font-family: Source Han Sans;
      font-size: var(--font-12);
      font-weight: normal;
      line-height: 18px;
    }
  }
}
</style>
