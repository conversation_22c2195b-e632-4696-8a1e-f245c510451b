import { http } from '@/services/http'
import { getCookie } from '@/utils/app-gateway'
/**
 * 更新会话标题
 * @param params
 * @returns {*}
 */
export const updateChatTitle = (data: { title: string; chatId: string;}) =>
  http({
    url: '/api/chatDialog/v1/updateChatDialogTitle',
    data,
    method: 'post'
  })
/**
 * 删除历史会话
 * @param params
 * @returns {*}
 */
export const deleteChatHistory = (params: {  chatId: string;}) =>
  http({
    url: `/api/chatDialog/v1/deleteChatDialogByChatId?chatId=${params.chatId}`,
    method: 'post'
  })
/**
 * 置顶历史会话
 * @param params
 * @returns {*}
 */
export const topChatHistory = (params: {  chatId: string;}) =>
  http({
    url: `/api/chatDialog/v1/toppingDialogByChatId?chatId=${params.chatId}`,
    method: 'post'
  })
/**
 * 取消置顶历史会话
 * @param params
 * @returns {*}
 */
export const cancelTopChatHistory = (params: {  chatId: string;}) =>
  http({
    url: `/api/chatDialog/v1/cancelToppingByChatId?chatId=${params.chatId}`,
    method: 'post'
  })
/**
 * 获取历史会话
 * @param params
 * @returns {*}
 */
export const getChatHistory = (data: { userId?:string, size?: string; title: string}) =>
  http({
    url: '/api/baseChat/v1/getDialogByUserId',
    data: {
      ...data,
      userId: getCookie()?.userId,
      size: data?.size ? data.size : 100
    },
    method: 'post'
  })
