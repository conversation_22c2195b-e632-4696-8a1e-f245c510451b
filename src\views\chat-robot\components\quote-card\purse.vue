<template>
  <div class="quote-card-item-purse">
    <div class="title">
      <div class="com-font t1"><span v-if="data.sortId" class="sort">{{  data.sortId }}</span>{{ data.unit }}</div>
      <span v-if="data.code" class="com-font t2">[编号：{{ data.code }}]</span>
    </div>
    <div class="com-font question">{{ data.question }}</div>
    <div class="com-font answer">
      <p>回答：</p>
      <p>{{ data.answer }}</p>
    </div>

    <div v-if="data.url" class="toolbar">
      <a-button type="link" class="com-font btn" @click="go">
        <template #icon>
          <svg-icon icon="icon-link2" class="w-[20px] h-[20px]" />
        </template>
        <span>链接</span>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@/components'

interface Props {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})

function go() {
  window.open(props.data.url, '_blank')
}
</script>

<style scoped lang="scss">
.quote-card-item-purse {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .title {
    display: flex;
    justify-content: space-between;
  }

  .com-font {
    font-family: Source Han Sans;
    font-size: var(--font-14);
    font-weight: normal;
    line-height: normal;
  }
  .sort {
    padding:0 4px;
    box-sizing: border-box;
    color: var(--fill-0);
    background-color: var(--main-5);
    border-radius: var(--border-radius-4);
    margin-right: 4px;
  }
  .t1 {
    font-family: 思源黑体;
    font-size: var(--font-12);
    font-weight: bold;
    color: var(--text-4);
  }

  .t2 {
    font-size: var(--font-10);
    line-height: 18px;
    color:var(--fill-6);
  }

  .question {
    font-size: var(--font-12);
    line-height: 18px;
    color: var(--text-4);
  }

  .answer {
    padding: 8px;
    border-radius: var(--border-radius-4);
    background: var(--fill-1);
    font-size: var(--font-12);
    line-height: 22px;
    color: var(--text-4);
    white-space: break-spaces;
  }

  .toolbar {
    width: 100%;
    display: flex;
    justify-content: flex-end;

    .btn {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 18px;
      color: var(--text-4);
      padding: unset;
      font-size: var(--font-12);
    }

    .btn:hover {
      color: var(--main-6);
    }
  }
}
</style>
