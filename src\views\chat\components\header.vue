<template>
  <div class="header">
    <div class="title">智能问答</div>

    <a-button type="link" class="btn" @click="onclickNewChat">
      <template #icon>
        <img :src="loadThemeImg('newchat.svg')"  alt="新建对话"  class="w-[16px] h-[16px]"/>
      </template>
      <span class="t">新建对话</span>
    </a-button>

    <a-button v-if="!isVisitor" type="link" class="btn" @click="showDrawer">
      <template #icon>
        <svg-icon icon="icon-chat-history" class="w-[16px] h-[16px]" />
      </template>
      <span class="t">历史对话</span>
    </a-button>
    <History v-model:visible="visible" />
  </div>
</template>

<script setup lang='ts'>
import { computed, ref } from 'vue'
import { message } from 'ant-design-vue'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { useMenusStore,useChatHistoryStore } from '@/store'
import { useRouter } from 'vue-router'
import { loadThemeImg } from '@/hooks/use-theme'
import { getCookie  } from '@/utils/app-gateway'
import History from '@/views/chat/components/history/index.vue'

defineOptions({
  name: 'ChatIndexHeader'
})
const router = useRouter()
// 系统信息
const menusStore = useMenusStore()
const systemInfo = computed(() => menusStore.systemInfo)
const bosssoftCookie = getCookie()
const isVisitor = computed(() => bosssoftCookie?.isVisitor)
const chatHistoryStore = useChatHistoryStore()
const { resetChat, state } = useChatGptInject()
// 新建对话
const onclickNewChat = () => {
  if(state.isLoading) {
    message.warn('正在回答中，请稍后')
    return
  }
  if (state.isNewChat) {
    message.warn('当前已是最新对话')
  } else {
    resetChat()
    chatHistoryStore.clearChatHistory()
    state.isNewChat = true
    router.replace({name: 'ChatIndex' })
  }
}
// 历史对话
const visible = ref(false)
const showDrawer = () => {
  visible.value = true
}

</script>
<style lang="scss" scoped>
.header {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  gap: 8px;
  height: 60px;
  padding: 0 0 24px 0;

  .title {
    width: 100%;
    font-family: Source Han Sans;
    font-size: var(--font-22);
    font-weight: bold;
    line-height: normal;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 36px;
    padding: 8px 12px;
    gap: 4px;

    .t {
      color: var(--text-5);
    }
  }
}

</style>
