<template>
  <div class="reader">
    <a-spin v-if="loading" :spinning="loading"></a-spin>
    <div class="tool-bar" :class="{'is-new-target': isNewTarget}">
      <div class="tool-bar-box">
        <div class="tool-bar-left">
          <a-tooltip placement="bottom">
            <template #title>退出</template>
            <svg-icon v-if="!isNewTarget" icon="icon-back" class="icon-btn" @click="onClickClose"/>
          </a-tooltip>
          <a-typography-paragraph
          v-if="apiParams.fileName || originParams.fileName"
          class="content"
          :content="apiParams.fileName || originParams.fileName"
          :ellipsis="{ rows: 1 }"/>
          <span v-if="apiParams.fileStatus == 1 ||originParams.fileStatus == 1 " class="base-invalid-tag tag">失效</span>
        </div>
        <div v-if="!isNewTarget" class="close" @click="onClickClose"><svg-icon icon="icon-close" class="icon-btn"/></div>
      </div>
      <div class="tool-bar-box tool-bar-box-bottom">
        <div class="flex">
          <template v-if="isShowMenu && treeData.length > 0">
            <a-tooltip placement="bottom">
              <template #title>目录</template>
              <svg-icon  icon="icon-menu" class="icon-btn icon-menu" @click="toggleMenu"/>
            </a-tooltip>
          </template>
          <a-tooltip placement="bottom">
            <template #title>文件</template>
            <svg-icon v-if="fileList.length" icon="icon-folder" class="icon-btn" @click="toggleFolder"/>
          </a-tooltip>
        </div>
        <div class="btn-download">
          <a-tooltip placement="bottom">
            <template #title>下载</template>
            <svg-icon v-if="isShowDownload" icon="icon-download1" class="icon-btn" @click="onClickDown"/>
          </a-tooltip>
        </div>
      </div>
    </div>
    <!-- 滚动 40+12 -->
    <template  v-if="isShowFolder && fileList.length">
      <div class="folder-wrap">
        <base-swiper :data="fileList" slides-per-view="auto" @on-swiper-click="onSwiperClick">
          <template #default="{ row }">
            <a-tooltip placement="top">
              <template #title>{{row.fileName}}</template>
              <div class="file-item">
                <svg-icon :icon="getFileIcon(row)" class="icon-file"></svg-icon>
                <div class="file-name">{{row.fileName}}</div>
              </div>
            </a-tooltip>
          </template>
        </base-swiper>
      </div>
    </template>
    <div class="reader-body-wrap"  :class="{'hide-folder': !(isShowFolder && fileList.length)}">
      <div class="reader-body">
        <a-spin v-if="contentLoading" :spinning="contentLoading"></a-spin>
        <template v-else>
          <template v-if="isTemplate">
            <div class="reader-menu"  :class="{'is-hide': !(isShowMenu && showMenu) }">
              <a-tree
                v-if="treeData.length"
                v-model:selected-keys="selectedKeys"
                default-expand-all
                :field-names="{ children: 'childList', key: 'id', value: 'id', title: 'content' }"
                :tree-data="treeData"
                @select="goAnchor">
                <template #title="{ content }">
                  <a-tooltip>
                    <template #title>
                      {{ content }}
                    </template>
                    {{ content }}
                  </a-tooltip>
                </template>
              </a-tree>
            </div>
            <template v-if="templateHtml">
              <div class="render-template-wrap">
                <div class="reader-template">
                  <!-- 切点-->
                  <ul v-if="isShowSection"  class="location-wrap">
                    <li
                      v-for="(item,index) in sectionInfo"
                      :key="item"
                      :class="{'active': selectedKeys[0] === item}"
                      @click="goAnchor([item])">
                      {{ index+1 }} ”
                    </li>
                  </ul>
                  <div class="reader-template-content" :class="{'is-operate':isOperating}"  v-html="templateHtml"></div>
                </div>
              </div>
            </template>
            <base-empty v-else/>
          </template>
          <pdf-reader v-else-if="isNegatives" :url="originParams.previewUrl" :page="originParams.currentPage"  @load="closeIframeLoading"></pdf-reader>
          <iframe v-else-if="fileViewUrl" :src="fileViewUrl" width="100%" height="100%"  @load="closeIframeLoading" @error="alert('错误')"></iframe>
          <base-empty v-else/>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref , nextTick} from 'vue'
import { Base64 } from 'js-base64'
import { useRouter } from 'vue-router'
import  { useMenusStore } from '@/store'
import { getOperatingTemplate, getOperatingMenu, getTemplateFileList } from '@/api/chat'
import { apiDownRefs  } from '@/api/download'
import {  getCookie } from '@/utils/app-gateway'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import BaseSwiper from '@/components/BaseSwiper/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import PdfReader from '@/components/PdfReader/index.vue'
defineOptions({
  name: 'FileReader'
})

type Props = {
  isNewTarget?: boolean // 是否新页面打开
}
const props = withDefaults(defineProps<Props>(), {
  isNewTarget: true
})
const router = useRouter()
const menusStore = useMenusStore()
const bosssoftCookie = getCookie()
const { state } =  props.isNewTarget ? {} : useChatGptInject()
// 引用切片参数
const originParams = computed(()=> {
  let params = {} as Record<string,any>
  if(state) {
    params =  state.originParams
  }else {
    const query = router.currentRoute.value?.query?.query ? JSON.parse(decodeURIComponent(router.currentRoute.value?.query?.query))  :  {}
    params = query
    if(params.appId) menusStore.setAppId(params.appId)
  }
  return params
})
// 是否法规
const isRaw = computed(()=> {
  const val = originParams.value.sectionType
  return val && val.includes('clean_regulations')
})
// 是否流程图
const isFlowChart = computed(()=> originParams.value.sectionType && originParams.value.sectionType.includes('clean_flowcharts'))
// 是否操作手册
const isOperating = computed(()=> originParams.value.sectionType && originParams.value.sectionType.includes('clean_operating_manual'))
// 是否负面清单
const isNegatives = computed(()=> originParams.value.sectionType && originParams.value.sectionType.includes('clean_negatives'))
const apiParams = ref<Record<string,any>>({})
// 获取文件列表
const fileList = ref<any[]>([])
const getFileList = async () => {
  fileList.value = []
  const { err, data } = await getTemplateFileList({
    type: originParams.value.sectionType,
    segmentIds: originParams.value.sectionInfo
  })
  if(err) return
  fileList.value = data ?? []
  apiParams.value = data?.[0] ?? {}
}
const getFileIcon = (row: any)=> {
  if(row.suffixType == 1) return 'icon-pdf'
  else if(row.suffixType == 2) return 'icon-doc'
  else return 'icon-file'
}
// 点击文件
const onSwiperClick = (row:any)=> {
  apiParams.value = row ?? {}
  initBody()
}
// 模版数据
const templateHtml = ref<string>('')
const getTemplate = async () => {
  templateHtml.value = ''
  const { err, data } = await getOperatingTemplate({
    type: originParams.value.sectionType,
    fileId:  isRaw.value ? apiParams.value.fileId  : '',// 只有法规传fieldId
    segmentIds: originParams.value.sectionInfo
  })
  if (err) return
  if(data) {
    templateHtml.value = data ?? ''
    // const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])/ig
    // const urlRegex = /(?<!<a[^>]*href="|href=')(?<!<img[^>]*src="|src=')(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])/ig
    // templateHtml.value = templateHtml.value.replace(urlRegex, (match) => {
    //   // 返回替换后的超链接
    //   return `<a href="${match}" target="_blank">${match}</a>`;
    // })
  }
}
// 走马灯-目录
const isShowFolder = ref(true)
const toggleFolder = ()=> {
  isShowFolder.value = !isShowFolder.value
}
// 返回----
const onClickClose = () => {
  if(state.originParams.sortId) state.isShowQuoteSearch = true
  else state.isShowQuote = true
  state.isShowOriginal = false
}
// 下载,法规外链无下载,游客无下载
const isShowDownload = computed(()=> {
  return !bosssoftCookie?.isVisitor &&((isRaw.value && apiParams.value.downloadFileId) || (!isRaw.value && originParams.value.fileId))
})
const onClickDown = async () => {
  const fileId = apiParams.value.downloadFileId || originParams.value.fileId
  apiDownRefs(fileId)
}
// 流程图、操作手册、法规中displayType == 1用模版，清单用pdf ，其他用kkfileview
const isTemplate = computed(()=> {
  return isOperating.value || (isRaw.value && apiParams.value.displayType == 1) || isFlowChart.value
})
// 左右折叠菜单-默认折叠-操作手册和法规
const isShowMenu = computed(()=> isOperating.value || isRaw.value)
const showMenu = ref(false)
const toggleMenu = ()=> {
  showMenu.value = !showMenu.value
}
// 树形折叠
const selectedKeys = ref<string[]>([])
const cacheKeys = ref<string[]>([])
const treeData = ref<any[]>([])
const goAnchor = (select: string[] , el?: any) => {
  const { key } = el?.node ?? ''
  if (key && key === cacheKeys.value) selectedKeys.value = [key]
  else {
    selectedKeys.value = select
    cacheKeys.value = key
  }
  const val = selectedKeys.value?.[0]
  nextTick(()=>{
    document.getElementById(`tree-node-${val}`)?.scrollIntoView()
  })
}
// 获取树菜单数据
const getMenu = async () => {
  treeData.value = []
  const { err, data } = await getOperatingMenu({
    type: originParams.value.sectionType,
    fileId: isRaw.value ? apiParams.value.fileId  : '',// 只有法规传fieldId
    segmentIds:  originParams.value.sectionInfo
  })
  if (err) return
  treeData.value = data ?? []
}
// kkFileview地址
const fileViewUrl  = computed(()=> {
  const url = originParams.value.previewUrl || apiParams.value.fileUrl
  const previewUrl = url? encodeURIComponent(Base64.encode(url)) : ''
  if(!previewUrl) return ''
  return import.meta.env.VITE_APP_FILE_VIEW_URL+ '?url='+previewUrl+'&officePreviewType=pdf&tifPreviewType=pdf'+'&page='+(originParams.value.currentPage||1)
})
// 切点，法规用fileList的值，其余用引用依据参数
const isShowSection = computed(()=> sectionInfo.value?.length && !contentLoading.value && apiParams.value.displayType == 1)
const sectionInfo = computed(()=>{
  return isRaw.value ? (apiParams.value.segmentIds ??[]) : originParams.value.sectionInfo
})

// 初始化
const loading = ref(false)
const contentLoading = ref(false)
const init = async () => {
  loading.value = true
  // 只有法律法规有文件列表
  if(isRaw.value) await getFileList()
  initBody()
}
const initBody = async ()=> {
  contentLoading.value = true
  // 只有操作手册和法规有目录
  if(isShowMenu.value) await getMenu()
  // 操作手册、法规、流程图定制模版
  if(isTemplate.value) await getTemplate()
  contentLoading.value = false
  // 有带定位信息跳转到定位-法规是fileList里的参数，其他用切片的切点
  if(sectionInfo.value?.length) {
    const key = sectionInfo.value[0]
    if(key) goAnchor([key])
  }
  // 使用模版的接口请求完就关闭，负面清单和kk的等iframe回调后关闭
  if(!(isNegatives.value || fileViewUrl.value))  loading.value = false
}
const closeIframeLoading = ()=> {
  setTimeout(()=> {
    loading.value = false
  },1000)
}
init()
</script>
<style lang="scss" scoped>
.ant-spin {
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reader {
  min-width: 450px;
  height: 100%;
  overflow: hidden;
  border: 1px solid var(--line-3);
  border-radius: var(--border-radius-16);
  flex: 1;
  position: relative;
  .tool-bar {
    &.is-new-target {
      :deep(.ant-typography) {
        max-width: initial;
      }
    }
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 95px;
    padding: 16px;
    background: var(--fill-0);
    border-bottom: 1px solid var(--line-3);
    box-sizing: border-box;
    :deep(.ant-typography) {
      margin-bottom: 0;
      max-width: 30vw;
      font-family: 思源黑体;
      font-size: var(--font-16);
      font-weight: bold;
    }
    .tool-bar-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &.tool-bar-box-bottom {
        margin-top: 16px;
      }
    }
    .tool-bar-left {
      display: flex;
      align-items: center;
    }
    .icon-menu {
      margin-right: 10px;
    }
    .icon-back,
    .icon-btn {
      width: 24px;
      height: 24px;
    }
    .icon-btn {
      &:hover {
        background-color: var(--fill-1);
      }
    }
    .svg-icon {
      cursor: pointer;

      &:focus {
        outline: none;
      }
    }
  }
  .location-wrap {
    position: absolute;
    right: 18px;
    // top:234px;
    top: 127px;
    z-index: 2;
    &.hide-folder {
      top:182px;
    }
    li {
      cursor: pointer;
      width: 80px;
      height: 68px;
      line-height: 24px;
      padding: 16px 0 0 22px;
      margin-bottom: 4px;
      text-align: center;
      box-sizing: border-box;
      font-family: 思源黑体;
      font-size: var(--font-14);
      font-weight: bold;
      color: var(--main-4);
      overflow: hidden;
      background: url('@/assets/images/location.png')  no-repeat center center;
      background-size: 100% 100%;
      &.active {
        color: var(--text-0);
        background-image: url('@/assets/images/location-active.png');
      }
    }
  }
  :deep(.folder-wrap) {
    position: absolute;
    left: 0;
    top: 95px;
    z-index: 1;
    width: 100%;
    padding: 12px 0 0 16px;
    &.hide {
      display: none;
    }
    .file-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      gap: 4px;
      background: var(--fill-0);
      box-sizing: border-box;
      border: 1px solid var(--line-3);
      border-radius: var(--border-radius-8);
      color: var(--text-2);
      font-weight: normal;
      .icon-file {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
      }
      .file-name {
        font-family: Source Han Sans;
        font-size: var(--font-12);
        max-width: 228px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .swiper-slide{
      width: auto;
      &.is-active {
        .file-item {
          color: var(--text-3);
          font-weight: bold;
        }
      }
    }
    .swiper-button-prev {
      left: 0;
    }
    .swiper-button-next {
      right: 0;
    }
  }
  .reader-body-wrap {
    padding: 159px 0 0 12px;
    height: 100%;
    background: var(--fill-1);
    box-sizing: border-box;
    overflow: hidden;
    &.hide-folder {
      padding: 107px 0 0 12px;
    }
  }
  .reader-body {
    display: flex;
    height: 100%;
    overflow: hidden;
  }

  .reader-menu {
    flex-shrink: 0;
    padding-right:12px;
    overflow: hidden;
    overflow-y: scroll;
    &.is-hide {
      display: none;
    }
    :deep(.ant-tree) {
      background: var(--fill-1);

      .ant-tree-treenode {
        width: 240px;
        color: var(--text-3);
        padding: 0;
        margin-bottom: 4px;
        box-sizing: border-box;
        height: 24px;
        line-height: 24px;

        .ant-tree-title {
          font-size: var(--font-12);
          font-weight: normal;
          font-family: Source Han Sans;
        }

        &:hover,
        &.ant-tree-treenode-selected {
          border-radius: var(--border-radius-4);
        }

        .ant-tree-switcher {
          margin-top: -2px;
        }

        .ant-tree-node-content-wrapper,
        .ant-tree-checkbox+span {

          // width: 100%;
          &:hover {
            background-color: var(--fill-3);
          }
        }
      }

      .ant-tree-title {
        padding: 0 4px;
      }

      .ant-tree-node-selected {
        background-color: var(--fill-3);
      }

      .ant-tree-node-content-wrapper {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .render-template-wrap {
    flex: 1;
    font-size: var(--font-12);
    box-sizing: border-box;
    background: var(--fill-1);
    font-family: Source Han Sans;
    word-break: break-all;
    display: flex;
    justify-content: center;
  }
  .reader-template {
    position: relative;
    max-width: 1200px;
    width: 100%;
    height: 100%;
    .reader-template-content{
      width: 100%;
      height: 100%;
      &.is-operate {
      :deep(.tree-container) {
          .tree-body {
            &.is-leaf {
              white-space: break-spaces;
            }
          }
        }
      }
    }
    :deep(.tree-container) {
      display: flex;
      border-radius: var(--border-radius-12);
      height: 100%;
      width: 100%;
      .tree-scroll-container {
        padding-right: 6px;
        margin-right: 6px;
        height: 100%;
        overflow-y: scroll;
        width: 100%;
        overflow-x: hidden;
        .tree-content {
          padding:64px 48px;
          box-sizing: border-box;
          border-radius: var(--border-radius-16);
          border: 1px solid var(--line-3);
          background-color: var(--fill-0);
          line-height: 24px;
          min-height: calc(100% - 12px );
          .is-highlight {
            color: var(--main-6);
          }
          .is-indent{
            text-indent: 2em;
          }
        }
        .note-title,
        .tree-title {
          font-family: 思源黑体;
          font-size: var(--font-14);
          font-weight: bold;
        }
        // 通知
        .note-title{
          font-family: SourceHanSerifC;
          text-align: center;
        }
        .note-file,
        .note-content,
        .note-title-center,
        .note-title-right {
          font-family: SongtiGb2312;
        }
        .note-title {
          font-size: var(--font-18);
        }
        .note-sub-title {
          margin: 16px 0;
          text-indent: 2em;
        }
        .note-file {
          margin-top: 16px;
        }
        // 大标题
        .tree-content>.tree-title {
          font-size: var(--font-18);
          text-align: center;
          line-height: normal;
        }
        .tree-content>.note-sub-title {
          margin: 0;
        }
        .note-title-right,
        .note-title-center,
        .note-content,
        .tree-title-right,
        .tree-body,
        .tree-title-center {
          margin-top: 16px;
          font-weight: normal;
          font-size: var(--font-14);
          color: var(--text-4);
        }
        .tree-cell{
          margin-top: 16px;
          text-indent: 2em;
          strong {
            margin-right: 16px;
          }
          &:first-child {
            margin-top: 0;
          }
        }
        .note-title-center,
        .tree-title-center {
          text-align: center;
        }
        .note-title-right,
        .tree-title-right {
          text-align: right;
        }
        .note-title-right {
          white-space: break-spaces;
        }
        // 正文
        .note-content,
        .tree-body {
          &.is-leaf {
          }

          img {
            display: inline-block;
            margin: 10px 0;
          }
        }
      }
    }
  }
}
</style>
