<template>
    <div class="sort-number">
        <span class="value">{{ content }}</span>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive } from 'vue'
defineOptions({
    name: 'ChatIndexSortNumber'
})

type Props = {
    content: number | null | undefined
}
const props = withDefaults(defineProps<Props>(), {
    content: null
})
</script>

<style lang="scss" scoped>
.sort-number {
    position: absolute;
    left: 0;
    top: 0;
    color: var(--text-0);
    font-size: var(--font-16);
    border-radius: var(--border-radius-12) 0 0 0;
    background-color: var(--main-6);
    box-sizing: border-box;
    min-width: 40px;
    min-height: 40px;
    overflow: hidden;
    padding:5px 0 10px 10px;
    font-weight: bold;
    clip-path: polygon(0 0, 0% 100%, 100% 0);
    .value {
        max-width: 26px;
        margin-right: 35px;
        text-align: center;
        display: block;
        overflow: hidden;
        white-space: nowrap;
        height: 20px;
        line-height: 20px;
    }
}
</style>
