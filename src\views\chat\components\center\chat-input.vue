<template>
  <div class="chat-input-box">
    <associate-popover ref="associateRef" v-model="associateVisible" />
    <div class="chat-input-container">
      <a-textarea
        v-model:value="state.inputVal"
        class="textarea !shadow-none"
        placeholder="输入你想了解的内容，Shift Enter换行，Enter发送"
        :readonly="state.disableAllInput || state.isLoading" :maxlength="1000"
        @press-enter="handleEnter"
      />
      <div v-if="state.disableAllInput" class="button-group">
        <div class="button btn-disabled">
          <SvgIcon class="icon" :icon="iconMicDefault" color="var(--fill-5)"/>
        </div>
        <div class="button btn-disabled">
          <SvgIcon class="icon" :icon="iconSendDisabled" color="var(--fill-5)"/>
        </div>
      </div>

      <div v-else class="button-group">
        <template v-if="state.isLoading">
          <a-button type="primary" ghost class="btn-stop-answer" @click="stopAnswer">
            <template #icon>
              <svg-icon icon="icon-chatinput-stop" class="icon-stop-answer" />
            </template>
            <span>停止回答</span>
          </a-button>
        </template>
        <template v-else>
          <div v-if="isYuYinStart" class="yu-yin-loading">
            <Lottie loop width="100%" height="100%" :json-data="MicWaveJson" />
          </div>
          <a-tooltip :title="isYuYinStart ? '停止' : '语音输入'">
            <div class="button" @click="clickMic()">
              <SvgIcon :icon="iconMic" class="icon"/>
            </div>
          </a-tooltip>
          <div class="button" :class="state.inputVal ? '' : 'btn-disabled'" @click="beforeSendQuestion()">
            <SvgIcon class="icon" :icon="iconSend"/>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useDebounceFn } from '@vueuse/core'
import { SvgIcon, Lottie } from '@/components/'
import { useMenusStore } from '@/store'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { XfVoiceDictation } from '@/utils/xf-voice-dictation/main.mjs'
import { getCookie } from '@/utils/app-gateway'
import config from '@/config'
import MicWaveJson from '@/assets/json/mic-wave.json'
import AssociatePopover from './associate-popover.vue'

defineOptions({
  name: 'ChatIndexCenterChatInput'
})
type Props = {
  isChatRobot?:boolean // 是否内嵌页
}
const props = withDefaults(defineProps<Props>(), {
  isChatRobot: false
})
const { state, currentQaMap,resetParam, stopAnswer, sendQuestion } = useChatGptInject()
// 系统信息
const menusStore = useMenusStore()
// 录音
const bosssoftToken = getCookie()
const voice = new XfVoiceDictation({
  url: config.xfGatewayPath + '/xf/v2/iat',
  APPID: 'a5e47c6e',
  token: 'Bearer ' + bosssoftToken?.token,
  vad_eos: 5 * 1000,
  //监听录音状态变化
  onWillStatusChange: function (oldStatus: string, newStatus: string) {
    if (newStatus == 'end') {
      isYuYinStart.value = false
      iconMic.value = iconMicDefault
    }
  },
  //监听识别结果变化
  onTextChange: function (text:string) {
    if (isYuYinStart.value) {
      state.inputVal = text
    }
  },
  onError: function (error: any) {
    isYuYinStart.value = false
    iconMic.value = iconMicDefault
    if (typeof(error) === 'string' && error.includes('录音权限获取失败')) {
      message.error('未授权浏览器录音权限，请检查您的浏览器设置，确保已经允许本平台访问您的麦克风。')
    } else {
      console.log(error,'录音权限获取失败')
    }
  }
})
// 按钮
const iconMicDefault = 'icon-chatinput-mic'
const iconSendDefault = 'icon-chatinput-send'
const iconSendDisabled = 'icon-chatinput-send-disabled'
const iconSend = computed(() => state.inputVal ? iconSendDefault : iconSendDisabled)
const iconMic = ref(iconMicDefault)
const isYuYinStart = ref(false)

function clickMic() {
  isYuYinStart.value = !isYuYinStart.value
  if (isYuYinStart.value) {
    voice.start()
    iconMic.value = 'icon-chatinput-mic-stop'
  } else {
    if (voice.status == 'end') {
      voice.stop()
    }
    iconMic.value = iconMicDefault
  }
}

// 输入
const handleEnter = (event: any)=> {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    beforeSendQuestion()
  }
}
const associateVisible = ref(false)
const associateRef = ref()
// 检验输入值是否为空
const checkInputValueIsEmpty= () => {
  return !state.inputVal || state.inputVal === '' || state.inputVal.trim() === ''
}
/** 发送问题前校验 */
const beforeSendQuestion = ()=> {
  associateVisible.value = false
  if(currentQaMap.value.loading || state.disableAllInput) return
  isYuYinStart.value = false
  iconMic.value = iconMicDefault
  if (voice.streamRef.length != 0) {
    voice.stop()
  }
  resetParam()
  state.question = state.inputVal
  if(checkInputValueIsEmpty() && !state.question) {
    message.warn('请输入问题')
    return
  }
  state.isNewChat = false
  const obj = state.associateInputList.find((x:any)=> x.question === state.inputVal) ?? {}
  state.similarQuestionId = obj.questionId ?? ''
  sendQuestion(state.inputVal)
  state.inputVal = ''
}

// 加载联想问题
const fetchSimilarList = useDebounceFn(async () => {
  if(checkInputValueIsEmpty()) return
  await associateRef.value?.getSimilarList(state.inputVal)
  associateVisible.value = true
}, 500)

watch(() => state.inputVal, (val) => {
    associateVisible.value = false
    if (!checkInputValueIsEmpty() && val.trim().length >= 2) {
      fetchSimilarList()
    }
},
{
  immediate: true
})
</script>

<style lang="scss" scoped>
.chat-input-box {
  position: relative;
  width: 100%;
  height: 86px;
  margin-top: 32px;

  .chat-input-container {
    width: 100%;
    height: 100%;
    padding: 12px 16px;
    border: 1px solid var(--main-3);
    border-radius: var(--border-radius-12);
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: flex-end;

    .textarea {
      width: 100%;
      height: 100%;
      resize: none;
      font-family: Source Han Sans;
      font-weight: normal;
      line-height: normal;
      color: var(--text-5);
      overflow-y: auto;
      border-style: none !important;
      padding: 0 !important;
    }

    .textarea::-webkit-scrollbar {
      display: none;
    }

    .textarea::placeholder {
      font-family: Source Han Sans;
      font-weight: normal;
      line-height: normal;
      color: var(--text-2);
    }

    .button-group {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 12px;

      .yu-yin-loading {
        width: 60px;
        height: 20px;
      }

      .button {
        cursor: pointer;

        .icon {
          width: 30px;
          height: 30px;
          fill: var(--main-6);

          &:hover {
            fill: var(--main-5);
          }
        }
      }

      .btn-stop-answer {
        display: flex;
        gap: 4px;
        align-items: center;

        &:hover {
          color: var(--main-5);
          border: 1px solid var(--main-5);

          .icon-stop-answer {
            fill: var(--main-5);
          }
        }

        .icon-stop-answer {
          width: 16px;
          height: 16px;
          fill: var(--main-6);
        }
      }

      .btn-disabled {
        cursor: not-allowed !important;

        .icon {
          fill: var(--fill-5);

          &:hover {
            fill: var(--fill-5);
          }
        }
      }
    }
  }
}

</style>
