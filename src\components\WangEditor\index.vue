<template>
  <div class="wang-editor">
    <toolbar class="toolbar" :editor="refEditor" :default-config="toolbarConfig" :mode="mode"/>
    <editor v-model="htmlVal" class="editor" :default-config="editorConfig" :mode="mode" @on-created="doCreated"/>
  </div>
</template>
<script setup lang="ts">
import {computed, onBeforeUnmount, ref, shallowRef} from 'vue'
import {Editor, Toolbar} from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'

/**
 * html html内容
 */
type Props = {
  html: string
}

const props = withDefaults(defineProps<Props>(), {
  html: ''
})
const emit = defineEmits(['update:html'])
const htmlVal = computed({
  get() {
    return props.html
  },
  set(val) {
    emit('update:html', val);
  }
})

// 编辑器实例
const refEditor = shallowRef()
const toolbarConfig = {}
const editorConfig = {placeholder: '请输入内容...'}
const mode = ref('default')

onBeforeUnmount(() => {
  // 组件销毁时，也及时销毁编辑器
  const editor = refEditor.value
  if (editor) {
    editor.destroy()
  }
})

function doCreated(editor: any) {
  refEditor.value = editor
}

defineExpose({
  refEditor
})
</script>
<style scoped lang="scss">
.wang-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);

  .toolbar {
    border-bottom: 1px solid var(--line-1);
    height: 48px;
  }

  .editor {
    height: calc(100% - 48px) !important;
  }
}
</style>
