<template>
  <div v-if="text" class="ai-search">
    <div  class="state" @click="toggleExpand">
      <FileSearchOutlined class="icon-search"/>
      <div class="t"> {{loading ?'知识库检索中...':'检索完成'}}（{{len}}条）</div>
      <SvgIcon icon="icon-arrow-down" class="icon-arrow-up" :class="expand? '':'icon-arrow-down'" />
    </div>
    <div v-if="expand" class="tag-box">
      <div v-if="searchTags.length" class="line" />
      <template v-for="(tag,index) in searchTags" :key="index"> 
          <a-checkable-tag
          :checked="state.selectTag === tag.key"
          @change="checked => handleChange(tag)"
          >
          <SearchOutlined />
          {{ tag.name }}（{{ tag.size || 0}}）条
          </a-checkable-tag>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, reactive } from 'vue'
import { v4 as uuid } from 'uuid'
import { FileSearchOutlined, SearchOutlined } from  '@ant-design/icons-vue'
import { SvgIcon } from '@/components'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
type Props = {
  text: string,
  loading: boolean
}
const props = withDefaults(defineProps<Props>(), {
  text: '',
  loading: false
})
const emit = defineEmits(['openQuoteSearch'])
const { state } = useChatGptInject()
const expand  = ref(true)
const toggleExpand =()=> {
    expand.value = !expand.value
}
const searchTags = computed(()=> {
  const text = props.text
  if(!text) return []
  let arr = JSON.parse(text)
  arr = arr.map(x=> {
    return {
      ...x,
      key: x.key || uuid()
    }
  })
  return arr
})
const handleChange = (tag: Record<string,any>) => {
    state.selectTag = tag.key
    emit('openQuoteSearch', tag)
}
const len = computed(()=> searchTags.value.reduce((sum: number, item: any) => sum + (item.size || 0), 0))
</script>

  <style scoped lang="scss">
  :deep(.loading) {
    &.ant-spin-spinning {
      text-align: left;
    }
  }

  .ai-search {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .state {
      width: fit-content;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--fill-2);
      border-radius: 10px;
      padding: 7px 14px;
      cursor: pointer;

      &:hover {
        background: var(--fill-3);
      }

      .icon-search {
        width: 12px;
        height: 12px;
        margin-right: 8px;
      }

      .icon-arrow-up {
        width: 10px;
        height: 10px;
        transform: rotate(180deg);
      }

      .icon-arrow-down {
        transform: rotate(0);
      }

      .t {
        font-size: 12px;
      }
    }

    .tag-box {
        position: relative;
        padding: 0 0 0 13px;
      :deep(.ant-tag-checkable) {
        border-color: inherit;
        line-height: 24px;
        &.ant-tag-checkable-checked{
          background-color: var(--main-6);
        }
      }
      :deep(.ant-tag) {
        line-height: 24px;
        margin-bottom: 10px;
      }
      .line {
            position: absolute;
            top: 0;
            left: 0;
            height: calc(100% - 10px);
            border-left: 2px solid var(--fill-1);
            margin-top: 5px;
        }
    }
  }
  </style>
