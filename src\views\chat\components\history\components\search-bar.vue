<template>
  <div class="input-container">
    <a-input placeholder="搜索历史记录" class="input" allow-clear v-model:value="searchValue">
      <template #prefix>
        <SvgIcon icon="icon-history-search" class="!h-[14px] !w-[14px] mr-[8px]" />
      </template>
    </a-input>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import {SvgIcon} from "@/components/";

const props = defineProps({
  inputValue: {
    default: '',
    type: String
  }
})
const searchValue = ref(props.inputValue)
const emits = defineEmits(['update:inputValue'])

defineExpose({
  clear
})

watch(() => searchValue.value, (val) => {
  emits('update:inputValue', val)
})

function clear(){
  searchValue.value = ''
}

</script>

<style lang="scss"  scoped>
.input-container {
  width: 100%;
  height: 40px;

  .clear {
    cursor: pointer
  }

  .input {
    border-radius: var(--border-radius-8);
    padding: 8px 16px;
    background: var(--fill-1);
    border: 0;
    font-size: var(--font-14);
    color: var(--text-5);

    :deep(.ant-input) {
      background: unset;
    }

    input::placeholder {
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      color: var(--text-2);
    }
  }

  .ant-input-affix-wrapper:focus,.ant-input-affix-wrapper-focused {
    border: none !important;
    box-shadow: none !important;
  }

  .ant-input {
    background-color: transparent;
  }
}
</style>
