export function valueFormat(value, formatType) {
  if (value === null || value === '' || value === undefined) {
    return ''
  }
  if (formatType == 'money') {
    return `${Number(value.toFixed(2)).toLocaleString('zh-CN', {  currency: 'CNY', minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }else if (formatType == 'score') {
    return `${Number(value.toFixed(2)).toLocaleString('zh-CN', {  minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  } else if (formatType == 'yesOrNo') {
    return value ? '是' : '否'
  }
  return value
}

export function cellFormat(value, formatType){
  if (formatType == 'money') {
    return {
      style: {
        'text-align': 'right'
      }
    }
  }
  if (formatType == 'center') {
    return {
      style: {
        'text-align': 'center'
      }
    }
  }
  if (formatType == 'left') {
    return {
      style: {
        'text-align': 'left'
      }
    }
  }
  return {
    style: {}
  }
}
