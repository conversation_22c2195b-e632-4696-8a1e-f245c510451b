// 个性化配置常量
import antTheme from './ant-theme'
export const SystemInfo: Record<string,any> = {
  tianda: {
    htmlTitle: '天津大学智慧采购大模型',
    htmlLogo: `${import.meta.env.VITE_APP_PUBLIC_URL}/logo-tianda.png`,
    title: 'Hi，我是天津大学智慧采购大模型',
    footerVersion: '内容由天津大学智慧采购大模型生成，仅供参考',
    footerVersionSub: '技术支持：博思数采科技股份有限公司',
    answerTip: '由天津大学智慧采购大模型生成',
    antTheme:{
      token: {
        ...antTheme.tianda
      }
    }
  },
  default: {
    htmlTitle: '阳光公采大模型1.0',
    htmlLogo: `${import.meta.env.VITE_APP_PUBLIC_URL}/logo.png`,
    title: 'Hi，我是数采小招',
    footerVersion: '内容由 AI 生成，仅供参考',
    footerVersionSub: '',
    answerTip: '由阳光公采大模型生成',
    antTheme:{
      token: {
        ...antTheme.default
      }
    }
  }
}
