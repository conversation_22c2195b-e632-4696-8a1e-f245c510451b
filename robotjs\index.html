<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成测试页</title>
    <style>
        html, body {
            height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body>
    <label for="appId">appId:</label>
    <input type="text" id="appId" name="appId" placeholder="请输入" readonly value="1801428690100203522"><br>
    <label for="userId">用户id:</label>
    <input type="text" id="userId" name="userId" placeholder="请输入" required value="1"><br>
    <label for="username">用户名:</label>
    <input type="text" id="username" name="username" placeholder="请输入" required value="1"><br>
    <label for="orgName">组织名称:</label>
    <input type="text" id="orgName" name="orgName" placeholder="请输入" required value="1"><br>
    <label for="orgCode">组织代码:</label>
    <input type="text" id="orgCode" name="orgCode" placeholder="请输入" required value="1"><br>
    <label for="sign">签名:</label>
    <input id="sign" name="sign" rows="4" required value="bosssoft@1q2w3e4r"></input><br>

    <button id="show">显示窗口</button>
    <button id="hide">隐藏窗口</button>
</body>

<script src="../robotjsDist/index.js"></script>
<script>

  let chatRobot

  // 显示聊天窗
  document.querySelector("#show").addEventListener("mousedown", () => {
    chatRobot = new ChatRobot({
      appId: document.getElementById('appId').value,
      userId: document.getElementById('userId').value,
      username: document.getElementById('username').value,
      orgName: document.getElementById('orgName').value,
      sign: document.getElementById('sign').value,
      orgCode: document.getElementById('orgCode').value,
      onSignError: function () {
        console.log("sign error")
      },
    })
    chatRobot.show()
  })

  // 隐藏聊天窗
  document.querySelector("#hide").addEventListener("mousedown", () => {
    chatRobot && chatRobot.hide()
  })


</script>

</html>
