<template>
  <div v-if="robotStore.defaultQuestionList?.length" class="w-full question-default">
    <div class="top">试试这样问我：</div>
    <div class="list">
      <div v-for="(item,index) in robotStore.defaultQuestionList" :key="index" class="base-font item" @click="doAsk(item.question)">
        <div class="title">
          <img class="!w-[32px] !h-[32px]" :src="getIcon(index+1)" alt=""/>
          <span class="text">{{item.title  }}</span>
        </div>
        <a-typography-paragraph :content="item.question" :ellipsis="{ rows: 1 }" class="question" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { useRobotStore } from '@/store'

const { state, sendQuestion, resetParam } = useChatGptInject()
const getIcon = (index: number) => {
  return new URL(`../../../assets/images/icon-index-default-${index}.png`, import.meta.url).href
}
const robotStore = useRobotStore()
const doAsk = (val: string) => {
  resetParam()
  state.question = val
  state.inputVal = ''
  state.isNewChat = false
  sendQuestion(val)
}

robotStore.getDefaultQuestionList()
</script>

<style lang="scss" scoped>
.question-default {
  position: relative;
  display: flex;
  flex-direction: column;

  .top {
    margin-top: 40px;
    font-family: Source Han Sans;
    font-size: var(--font-14);
    font-weight: normal;
    color: var(--text-3);
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px;
    margin-left: -12px;

    .item {
      .title {
        display: flex;
        align-items: center;
        .text {
          font-size: var(--font-14);
          font-weight: bold;
          color: var(--text-5);
          margin-left: 8px;
        }
      }
      .question {
        font-size: var(--font-12);
        color: var(--text-3);
        margin-top: 4px;
        // 解决自适应问题
        white-space: normal;
        -webkit-line-clamp: 1;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      padding: 16px;
      border: 1px solid var(--line-2);
      border-radius: var(--border-radius-8);
      cursor: pointer;
      box-sizing: border-box;
      width: calc(33.3% - 12px);
      margin: 0 0 12px 12px;
      &:nth-child(1),
      &:nth-child(2) {
        width: calc(50% - 12px);
      }
      &:hover {
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 6px 16px 0px rgba(0, 0, 0, 0.08);
      }
    }
  }
}

</style>
