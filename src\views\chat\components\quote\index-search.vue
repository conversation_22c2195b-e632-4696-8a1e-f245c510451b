<template>
  <div class="quote" :class="state.isShowQuoteSearch ? '' : 'quote-hide'">
    <div  v-if="state.isShowQuoteSearch" class="quote-main">
      <div class="quote-top">
        <div class="t1">{{ tag.name || '检索' }}</div>
        <div class="t2">
          <span v-if="!loading">共{{ total }}个</span>
        </div>
        <a-button type="link" size="small" @click="doClose">
          <template #icon>
            <SvgIcon class="w-[20px] h-[20px]" icon="icon-close"/>
          </template>
        </a-button>
      </div>
      <template v-for="(list,key) in comList">
       <a-flex v-if="key && list?.length && getComType(key)" :key="key" vertical gap="24">
        <component :is="getComType(key)" v-for="(item,index) in list" :key="index" :chat-id="chatId" :data="item" />
       </a-flex>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref , watch } from 'vue'
import { SvgIcon } from '@/components'
import { refsSliceSummary } from '@/api/chat'
import { getComType }  from '@/views/chat/components/quote/hooks/use-data'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
defineOptions({
  name: 'ChatIndexQuoteSearch'
})

type Props = {
  chatId?: string
  quoteParam?: string
}

const props = withDefaults(defineProps<Props>(), {
  quoteParam: '',
  chatId: ''
})

const { state } = useChatGptInject()
// 关闭引用
const doClose= () =>  {
  state.selectTag = ''
  state.isShowQuoteSearch = false
}

// 初始化数据
const total = ref<number>(0)
const loading = ref(false)
const comList = ref<Record<string,any>>({})
const tag = ref<Record<string,any>>({})
const initData = (quoteParam:any)=> {
  loading.value = true
  total.value = 0
  comList.value = []
  if(!(quoteParam && quoteParam.values)) return
  tag.value = quoteParam
  const segmentIds = quoteParam.values ?? []
  const params = { segmentIds }
  refsSliceSummary(params).then((res) => {
      const {err, data} = res
      if (err) return
      total.value = data.total ?? 0
      comList.value =   data ?? {}
      loading.value = false
  })
}

defineExpose({
  loadQuoteSearch: initData
})

</script>
<style scoped lang="scss">
.quote {
  width: 420px;
  transition: 0.5s;

  .quote-main {
    height: 100%;
    padding: 16px;
    border-radius: var(--border-radius-16);
    box-sizing: border-box;
    border: 1px solid var(--line-3);
    display: flex;
    flex-direction: column;
    gap: 24px;
    overflow: auto;

    .quote-top {
      height: 24px;
      display: flex;
      align-items: center;
      gap: 8px;

      .t1 {
        font-size: var(--font-16);
        font-weight: bold;
      }

      .t2 {
        flex-grow: 1;
        font-family: Source Han Sans;
        font-size: var(--font-12);
        color: var(--text-4);
      }
    }
  }
}

.quote-hide {
  width: 0;
}
</style>
