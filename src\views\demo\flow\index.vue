<template>
    <iframe 
      ref="iframeRef"
      width="100%" 
      height="100%"
      src=""
 
    ></iframe>
  </template>
<script setup lang='ts'>
import { onMounted, ref } from 'vue';

const iframeRef = ref<HTMLIFrameElement>();

// 重写 window.open
const overrideWindowOpen = () => {
  const iframeWindow = iframeRef.value?.contentWindow;
  if (!iframeWindow) return;

  iframeWindow.open = (url: string) => {
    if (url && iframeRef.value) {
        console.log(url,'url')
    //   iframeRef.value.src = url; // 修改为在 iframe 内加载
      // 若要在当前页面打开：window.location.href = url;
    }
    return null; // 阻止新窗口
  };
};

onMounted(() => {
  iframeRef.value?.addEventListener('load', () => {
    try {
      overrideWindowOpen();
    } catch (e) {
      console.log('跨域限制，无法直接访问 iframe 内容'); 
    }
  });
});
</script>

