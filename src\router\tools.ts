import type { RouteRecordRaw, RouteLocationNormalizedGeneric, NavigationGuardNext } from 'vue-router'
import type {  BosssoftCookie } from '@/typings/login'
import { v4 as uuid } from 'uuid'
import config from '@/config'
// import {  getUserBusModuleCodes, saltConvert  } from '@/api/login'
import  { useMenusStore } from '@/store'
import { dynamicRoutesMap, fallbackRoutes } from './routes'
import { apiLogin } from '@/api/login'
import {  appLogin, appLoginOutHandler } from '@/utils/app-gateway'
import Layout from '@/layout/index.vue'
import ParentView from '@/layout/parent-view.vue'
import IframeView from '@/layout/iframe-view.vue'
// const currentSystemCode = config.route.currentSystemCode
// 设置系统权限
export const ensureRoutesData = async (router: any)=> {
  const menusStore = useMenusStore()
  if (menusStore.dynamicRoutesState) return true
  try {
    // const {err , data: result = []} = await getUserBusModuleCodes() ?? {}
    // if(err) return
    // const menusArr: any[] = []
    // const hasPermission = result.some(x=>x.code === currentSystemCode)
    // result.map((x: Record<string,any>)=> {
    //   let obj = {}
    //   const code = config.route.codeMap[x.code] ?? ''
    //   if(x.code === currentSystemCode){
    //     obj = {
    //       ...x,
    //       value: code,
    //       path: '/'+code
    //     }
    //     menusArr.push(obj)
    //   }
    // })
    // menusStore.setMenu(menusArr)
    // const dynamicsRoutes: any = hasPermission ? dynamicRoutesMap[currentSystemCode] : []
    // const routes: RouteRecordRaw[] = useDynamicsRoutes(dynamicsRoutes)
    const routes: RouteRecordRaw[] = useDynamicsRoutes([])
    // 访问无权限页面跳转地址
    routes.push(fallbackRoutes[0])
    routes.forEach((item: any) => {
      if (!router.hasRoute(item?.name)) {
        router.addRoute(item)
      }
    })
    menusStore.setDynamicRoutes(routes)
    menusStore.setDynamicRoutesState(true)
    return true
  } catch (e) {
    console.error('console.error', e)
    return false
  }
}
// 游客模式// 只在演示环境/开发环境下有游客模式
export const checkVisitorMode = async (to: RouteLocationNormalizedGeneric, from: RouteLocationNormalizedGeneric, next: NavigationGuardNext ) => {
  if(config.isTest || config.isDemo || config.isDev) {
      const visitorJumpKey = to.query?.visitorSalt as string
      if(visitorJumpKey==='a7f2c3d1-1e35-4b8f-9a12-bcde34fg5h67') {
        appLoginOutHandler()
        const { data = {}, err } = await apiLogin({
          telephone: '游客',
          visitorId: uuid()
        }) ?? {}
        if(err) {
          next({name: '500'})
          return
        }
        to.query = {}
        const bosssoftCookie: BosssoftCookie = {
          appId: data.appId,
          userId: data.uid,
          userName: data.userName,
          token: data.token,
          isVisitor: data.tourist,
          expireTime: data.expireTime,
          expireDateTime: new Date(data.expireDateTime)
        }
        appLogin(JSON.stringify(bosssoftCookie))
      }
  }
}
// 格式化菜单数据
const useDynamicsRoutes = (routes: any[]) => {
  return resolveRoutes(routes)

  // 解析系统菜单
  function resolveRoutes(routesList: any[]) {
    const res: any = []
    resolveRouteChildren(null, routesList)
    function resolveRouteChildren(parent: any, children?: any[]) {
      if (!children?.length) return
      // isValidRoute 逻辑用于去掉多级中没有子路由的二级菜单
      children.forEach((o: any) => {
        const { isValidRoute, ...item } = resolveRouteItem(o) || {}
        if (!isValidRoute) return
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.push(item)
          parent.isValidRoute = true
        } else {
          res.push(item)
        }
      });

    }
    function resolveRouteItem(route: any) {
      const { children, component, ...obj } = route
      // 用户权限里没有该路由，则不addRoute-暂时无
      const authCode = obj.meta?.authCode
      if (authCode) return null
      const item = { ...obj }
      if (component) item.component = resolveRouteComponent(component)
      if (component && !children?.length) {
        item.isValidRoute = true
      }
      resolveRouteChildren(item, children)
      return item
    }

    return res
  }

  function resolveRouteComponent(component?: any) {
    if (!component || typeof component !== 'string') return component
    if (component === 'Layout') {
      return Layout
    } else if (component === 'ParentView') {// 多级嵌套路由目录
      return ParentView
    } else if (component === 'IframeView') {// iframe
      return IframeView
    } else {
      return loadView(component)
    }
  }
}
// 匹配views里面所有的.vue文件
const modules = import.meta.glob('@/views/**/*.vue');
// const regComponentPath = /^\/src\/views\/(.+)\.vue$/g;
const loadView = (view: string) => {
  const res = modules[`/src/views/${view}.vue`] || null;
  return res;
}


