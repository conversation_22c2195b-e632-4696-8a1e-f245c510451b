<!-- 国库司 -->
<template>
  <div class="user-guide-item"  :class="{'has-sort': data.sortId}">
    <SortNumber v-if="data.sortId" :content="data.sortId"/>
    <div class="title-box">
      <div class="title">
        <span class="s1">{{ getCleanTypeTitle(data.sectionInfo) }}</span>
        <span v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span>
      </div>
      <a-button  type="primary" size="small" @click="readFile(data)">阅读原文</a-button>
    </div>

    <div class="file">
      <SvgIcon v-if="data.suffixType === 1" class="w-[24px] h-[24px]" icon="icon-word"/>
      <SvgIcon v-if="data.suffixType === 2" class="w-[24px] h-[24px]" icon="icon-ppt"/>
      <SvgIcon v-else class="w-[24px] h-[24px]" icon="icon-file"/>
      <span class="filename">{{ data.fileName }}</span>

      <a-tooltip>
        <template #title>点击下载</template>
        <a-button v-if="!isVisitor" type="link" size="small" @click="download">
          <template #icon>
            <div>
              <DownloadOutlined/>
            </div>
          </template>
        </a-button>
      </a-tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { SvgIcon } from '@/components'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { apiDownRefs } from '@/api/download'
import { getCleanTypeTitle } from './hooks/use-data'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import {  getCookie } from '@/utils/app-gateway'
import SortNumber from '@/views/chat/components/quote/sort-number.vue'
type Props = {
  chatId: string
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {
    sectionInfo: '',
    suffixType: 0
  }
})
const bosssoftCookie = getCookie()
const isVisitor = computed(() => bosssoftCookie?.isVisitor)
const { readFile } = useChatGptInject()

function download() {
  let fileId = props.data.fileId
  apiDownRefs(fileId)
}
</script>
<style scoped lang="scss">
.user-guide-item {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-radius: var(--border-radius-12);
  background: rgba(var(--fill-rbg-7),0.5);
  padding: 16px;
  &.has-sort{
    padding:30px 16px 16px 16px;
  }
  .title-box {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .title {
    display: flex;
    align-items: center;
    font-family: 思源黑体;
    font-weight: bold;
    line-height: normal;
    color: var(--text-4);
    flex-grow: 1;
  }

  .file {
    padding: 8px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    background: var(--fill-0);
    border-radius: var(--border-radius-8);

    .filename {
      flex-grow: 1;
      font-family: Source Han Sans;
      font-size: var(--font-12);
      font-weight: normal;
      line-height: 18px;
    }
  }
}
</style>
