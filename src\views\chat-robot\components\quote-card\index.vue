<template>
  <a-card v-if="visible" :id="idKey" :title="cardTitle" class="quote-card">
    <template #extra>
      <svg-icon icon="icon-arrow-up" class="quote-card-close w-[20px] h-[20px]" @click="onClose" />
    </template>

    <template v-for="(list,key) in comList">
      <a-flex v-if="key && list?.length" :key="key" vertical>
        <component :is="getCom(key)" v-for="(item,index) in list"  :key="index" :data="item"/>
      </a-flex>
    </template>
  </a-card>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from 'vue'
import { SvgIcon } from '@/components'
import { apiRefsDocument } from '@/api/chat'
import { v4 as uuid } from 'uuid'
import { refsSliceSummary } from '@/api/chat'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import Law from '@/views/chat-robot/components/quote-card/law.vue'
import Purse from '@/views/chat-robot/components/quote-card/purse.vue'
import UserGuide from '@/views/chat-robot/components/quote-card/user-guide.vue'
import Case from '@/views/chat-robot/components/quote-card/case.vue'

interface Props {
  visible: boolean
}

withDefaults(defineProps<Props>(), {
  visible: false
})
const { state } = useChatGptInject()
const emits = defineEmits(['update:visible'])
const comList = ref<Record<string, any>>({})
const comMap: Record<string, any> = {
  lawsList: Law,
  flowchartsList: UserGuide,
  negativesList: UserGuide,
  operatingManualList: UserGuide,
  treasuryDepartmentList: Purse,
  typicalCaseList: Case
}
const idKey = ref('quote-card-'+uuid())
const tag = ref<Record<string,any>>({})
const cardTitle = computed(()=> tag.value.name ||'引用内容')
function initData(data: any,isRobot?: boolean) {
  if(!data ) return
  tag.value = {}
  if(isRobot) {
    if(!data?.values) return
    tag.value = data ?? {}
  }
  const segmentIds = isRobot ?  data.values : data.split(',')
  const params = { segmentIds }
  const fn = isRobot ? refsSliceSummary : apiRefsDocument
  fn(params).then((res) => {
    const { err, data } = res
    if (err) return
    comList.value = data ?? {}
    nextTick(()=> {
      document.getElementById(idKey.value)?.scrollIntoView({
        behavior: 'smooth'
      })
    })
  })
}

function getCom(key: string) {
  return comMap[key] ?? Law
}

function onClose() {
  state.selectTag = ''
  emits('update:visible', false)
}

defineExpose({
  initData: initData
})
</script>

<style scoped lang="scss">
.quote-card {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid var(--line-1);
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
  .quote-card-close {
    cursor: pointer;
  }
}
</style>
