<template>
  <div class="quote-card-item-raw">
    <div class="com-font title"><span v-if="data.sortId" class="sort">{{  data.sortId }}</span><span>{{ data.fileName }}</span><span v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span></div>
    <div v-for="item in data.list" :key="item.id" class="com-font t">  {{ item.article }} {{ item.text }}</div>
    <div class="toolbar">
      <a-button v-if="data.url" type="link" class="com-font btn" @click="go">
        <template #icon>
          <svg-icon icon="icon-link2" class="w-[20px] h-[20px]" />
        </template>
        <span>链接</span>
      </a-button>
      <a-button type="link" class="com-font btn" @click="readFile(data,true)">
        <template #icon>
          <svg-icon icon="icon-view" class="w-[20px] h-[20px]" />
        </template>
        <span>查看原文</span>
      </a-button>
    </div>

    <a-divider v-if="1 < data.list.length" style="margin: 0 0 12px 0;" />
  </div>
</template>

<script setup lang="ts">
import { useChatGptInject } from '@/views/chat/hooks/use-chat'

interface Props {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})
const { readFile } = useChatGptInject()

function go() {
  window.open(props.data.url, '_blank')
}
</script>

<style scoped lang="scss">
.quote-card-item-raw {
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-bottom: 1px solid var(--line-1);
  margin-bottom: 12px;

  .com-font {
    font-family: Source Han Sans;
    font-size: var(--font-12);
    font-weight: normal;
    line-height: normal;
  }
  .sort {
    padding:0 4px;
    box-sizing: border-box;
    color: var(--fill-0);
    background-color: var(--main-5);
    border-radius: var(--border-radius-4);
    margin-right: 4px;
  }
  .title {
    font-weight: bold;
    line-height: 18px;
    color: var(--text-4);
  }

  .t {
    line-height: 18px;
    white-space: break-spaces;
    color: var(--text-4);
  }

  .toolbar {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    .btn {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 18px;
      color:var(--fill-6);
      padding: unset;
    }

    .btn:hover {
      color: var(--main-6);
    }
  }
}
</style>
