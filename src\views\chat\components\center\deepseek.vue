<template>
  <div v-if="text" class="deepseek">
    <div class="state" @click="toggleExpand">
      <SvgIcon icon="icon-deepseek" class="icon-deepseek" />
      <div class="t">{{loading ? '思考中...': (item.isTerminate && !item.thinkingTime) ? '思考已停止' : '已深度思考（用时'+(item.thinkingTime || 0) +'秒）'}}</div>
      <SvgIcon icon="icon-arrow-down" class="icon-arrow-up" :class="expand? '':'icon-arrow-down'" />
    </div>
    <div v-if="expand" class="body">
      <div v-if="text" class="line" />
      <div>{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { SvgIcon } from '@/components'
type Props = {
  text: string,
  loading: boolean,
  item: Record<string,any>
}
const props = withDefaults(defineProps<Props>(), {
  text: '',
  loading: false,
  item: ()=> {
    return {}
  }
})
const expand = ref(true)
function toggleExpand() {
  expand.value = !expand.value
}
</script>

<style scoped lang="less">
:deep(.loading) {
  &.ant-spin-spinning {
    text-align: left;
  }
}

.deepseek {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .state {
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--fill-2);
    border-radius: 10px;
    padding: 7px 14px;
    cursor: pointer;

    &:hover {
      background: var(--fill-3);
    }

    .icon-deepseek {
      width: 12px;
      height: 12px;
      margin-right: 8px;
    }

    .icon-arrow-up {
      width: 10px;
      height: 10px;
      transform: rotate(180deg);
    }

    .icon-arrow-down {
      transform: rotate(0);
    }

    .t {
      font-size: var(--font-12);
    }
  }

  .body {
    position: relative;
    color: var(--text-3);
    padding: 0 0 0 13px;
    white-space: pre-wrap;
    line-height: var(--line-height-24);

    .line {
      position: absolute;
      top: 0;
      left: 0;
      height: calc(100% - 10px);
      border-left: 2px solid var(--line-4);
      margin-top: 5px;
    }
  }
}
</style>
