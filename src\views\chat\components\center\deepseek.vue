<template>
  <div class="deepseek">
    <header @click="toggleVisible">
      <template v-if="visible">
        <DownOutlined />
        隐藏思考过程
      </template>
      <template v-else>
        <RightOutlined />
        显示思考过程
      </template>
    </header>

    <a-steps v-if="visible" direction="vertical" :current="currenStep">
      <template v-for="item in stepList" :key="item.id">
        <a-step @click="stepClick">
          <template #title>
            <a-collapse v-model:activeKey="activeKeys" expand-icon-position="end">
              <a-collapse-panel :key="item.id" :header="item.name">
                <div v-if="item.id.includes('3')" class="step-item-list">
                  <template v-for="cf in stepItemList" :key="cf.id">
                    <div class="step-item">
                      <div class="step-item-detail">
                        <div class="title">{{ cf.title }}</div>
                        <div class="content">{{ cf.content }}</div>
                      </div>
                      <a-button v-if="cf.count" type="primary" :icon="h(SearchOutlined)" shape="round" @click="doClick">
                        {{ cf.count }} 个文档
                      </a-button>
                    </div>
                  </template>
                </div>
                <template v-else>{{ item.description }}</template>
              </a-collapse-panel>
            </a-collapse>
          </template>
        </a-step>
      </template>
    </a-steps>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { DownOutlined, RightOutlined, SearchOutlined } from '@ant-design/icons-vue'
import TestStep from '../../../../../public/test-step.json'
import TestStepItemList from '../../../../../public/test-step-item-list.json'

const emits = defineEmits(['openQuote'])
const activeKeys = ref<string[]>(['1', '2', '3', '4'])
const visible = ref(true)
const panelList = ref()
const stepItemList = ref()

const currenStep = ref(2)
const stepList = ref(TestStep)

const stepClick = () => {

}

const toggleVisible = () => {
  visible.value = !visible.value
}

const doClick = () => {
  emits('openQuote', 'clean_regulations+103595')
}

onMounted(() => {
  panelList.value = TestStep
  stepItemList.value = TestStepItemList
})
</script>

<style scoped lang="scss">
.deepseek {
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: solid 1px #ccc;
  border-radius: 8px;
  margin: 0 16px 0 0;
  overflow: hidden;

  header {
    background: var(--fill-1);
    height: 48px;
    line-height: 48px;
    font-weight: bold;
    padding: 0 16px;
  }

  :deep(.ant-steps) {
    padding: 0 0 0 16px;

    .ant-steps-item-content {
      padding: 0 0 16px 0;

      .ant-steps-item-title {
        width: 100%;

        .ant-collapse-header-text {
          font-weight: bold;
        }

        .step-title {
          display: flex;

          .title {
            flex: 1;
          }
        }
      }
    }
  }

  //:deep(.ant-collapse) {
  //  background: unset;
  //  border: unset;
  //
  //  .ant-collapse-item {
  //    margin-bottom: 16px;
  //    border: solid 1px #ccc;
  //    border-radius: 8px;
  //    overflow: hidden;
  //
  //    .ant-collapse-header {
  //      background: var(--fill-1);
  //    }
  //
  //    .ant-collapse-header-text {
  //      font-weight: bold;
  //    }
  //  }
  //}

  .step-item-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .step-item {
      display: flex;
      gap: 16px;
      align-items: center;

      .step-item-detail {
        flex: 1;

        .title {
          font-weight: bold;
        }

        .content {
          font-style: italic;
          color: #848484;
        }
      }

      .ant-btn {
        span {
          display: inline-flex;
        }
      }
    }
  }
}
</style>
