import {defineConfig} from 'vite'
import path from 'path'
import {terser} from 'rollup-plugin-terser'

export default defineConfig({
    build: {
        rollupOptions: {
            // plugins: [terser()],
            input: {
                robotjs: 'robotjs/index.js'
            },
            output: {
                dir: 'robotjsDist',
                entryFileNames: 'index.js'
            }
        }
    },
    resolve: {
        alias: {
            '@': path.resolve(process.cwd(), 'src')
        }
    }
})
