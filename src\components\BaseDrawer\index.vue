<template>
  <a-drawer
    v-bind="$attrs"
    v-model:open="visible"
    :closable="closable"
    :placement="placement"
    :title="title"
    :width="width"
    @close="onClose"
  >

    <a-spin :spinning="loading">
      <slot></slot>
    </a-spin>

    <template #extra>
      <a-space>
        <a-button v-if="cancelVisible" @click="onCancel">{{ cancelText }}</a-button>
        <a-button v-if="okVisible" type="primary" @click="onOk">{{ okText }}</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const emits = defineEmits([
  'cancel',
  'close',
  'ok',
  'update:model-value'
])

/**
 * cancelText 取消按钮文字
 * okText 确认按钮文字
 * placement 方向 'top' | 'right' | 'bottom' | 'left'
 */
interface Props {
  modelValue: boolean
  cancelText?: string
  cancelVisible?: boolean
  closable?: boolean
  loading?: boolean
  okText?: string
  okVisible?: boolean
  placement?: 'top' | 'right' | 'bottom' | 'left'
  title?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  cancelText: '取消',
  cancelVisible: false,
  closable: true,
  loading: false,
  modelValue: false,
  okText: '确定',
  okVisible: false,
  placement: 'right',
  title: '',
  width: 'auto'
})

const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:model-value', val)
})

function onClose() {
  emits('update:model-value', false)
  emits('close')
}

function onCancel() {
  emits('cancel')
}

function onOk() {
  emits('ok')
}
</script>
