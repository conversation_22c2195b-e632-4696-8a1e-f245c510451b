import  { useMenusStore } from '@/store'
import { computed, watch } from 'vue'
import { SystemInfo } from '@/config'
// 目前只在咨询子应用单独访问时配置主题
export const useTheme = ()=>{
  const menusStore = useMenusStore()
  // const themeKey = computed(()=>  menusStore.isTianda ? 'tianda' : 'default')
  // 动态导入LESS文件---后期要替换
  // const themeClass = ref<any>(null)
  // // 动态引入样式文件
  // const loadTheme = async (themeName: string) => {
  //   try {
  //     const module = await import(`@/assets/css/modules/theme/${themeName}.scss?inline`)
  //     themeClass.value = module.default
  //   } catch (e) {
  //     console.error('Theme loading failed:', e);
  //   }
  // }
  // return { antTheme: SystemInfo[themeKey.value]?.antTheme}
  watch(()=> menusStore.themeKey,
(val)=> {
  const info = SystemInfo[val]
  // if(val && val!=='default') {
    const element = document.documentElement
    element.setAttribute('data-theme', val)
    // loadTheme(themeKey.value)
    // element.className += ` theme-is-${themeKey.value}`
  // }
  try {
    const link = document.querySelector('link[rel="icon"]')
    const title = document.getElementsByTagName("title")
    title[0].innerHTML = info.htmlTitle ?? '阳光公采大模型1.0'
    if(link) link.href = info.htmlLogo ?? '/logo.png'
  }
  catch(e) {
    console.log(e,'index.html报错')
  }
  menusStore.setSystemInfo(info)
},
{
  immediate: true
})
}
// 加载主题图片
export const loadThemeImg = (img: string) => {
  const menusStore = useMenusStore()
  const themeKey = computed(()=>  menusStore.isTianda ? 'tianda' : 'default')
  const res = new URL(`/src/assets/images/theme/${themeKey.value}/${img}`, import.meta.url).href
  return res
}
