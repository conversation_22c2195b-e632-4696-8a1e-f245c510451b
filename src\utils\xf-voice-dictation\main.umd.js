(function(J,s0){typeof exports=="object"&&typeof module<"u"?s0(exports):typeof define=="function"&&define.amd?define(["exports"],s0):(J=typeof globalThis<"u"?globalThis:J||self,s0(<PERSON><PERSON>ry<PERSON>={}))})(this,function(J){"use strict";function s0(H,R){for(var c=0;c<R.length;c++){const e=R[c];if(typeof e!="string"&&!Array.isArray(e)){for(const C in e)if(C!=="default"&&!(C in H)){const E=Object.getOwnPropertyDescriptor(e,C);E&&Object.defineProperty(H,C,E.get?E:{enumerable:!0,get:()=>e[C]})}}}return Object.freeze(Object.defineProperty(H,Symbol.toStringTag,{value:"Module"}))}var q=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ae(H){return H&&H.__esModule&&Object.prototype.hasOwnProperty.call(H,"default")?H.default:H}function ne(H){if(H.__esModule)return H;var R=H.default;if(typeof R=="function"){var c=function e(){return this instanceof e?Reflect.construct(R,arguments,this.constructor):R.apply(this,arguments)};c.prototype=R.prototype}else c={};return Object.defineProperty(c,"__esModule",{value:!0}),Object.keys(H).forEach(function(e){var C=Object.getOwnPropertyDescriptor(H,e);Object.defineProperty(c,e,C.get?C:{enumerable:!0,get:function(){return H[e]}})}),c}var or={exports:{}};function oe(H){throw new Error('Could not dynamically require "'+H+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var D0={exports:{}};const ie=ne(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var ir;function L(){return ir||(ir=1,function(H,R){(function(c,e){H.exports=e()})(q,function(){var c=c||function(e,C){var E;if(typeof window<"u"&&window.crypto&&(E=window.crypto),typeof self<"u"&&self.crypto&&(E=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(E=globalThis.crypto),!E&&typeof window<"u"&&window.msCrypto&&(E=window.msCrypto),!E&&typeof q<"u"&&q.crypto&&(E=q.crypto),!E&&typeof oe=="function")try{E=ie}catch{}var g=function(){if(E){if(typeof E.getRandomValues=="function")try{return E.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof E.randomBytes=="function")try{return E.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},u=Object.create||function(){function a(){}return function(n){var i;return a.prototype=n,i=new a,a.prototype=null,i}}(),l={},r=l.lib={},t=r.Base=function(){return{extend:function(a){var n=u(this);return a&&n.mixIn(a),(!n.hasOwnProperty("init")||this.init===n.init)&&(n.init=function(){n.$super.init.apply(this,arguments)}),n.init.prototype=n,n.$super=this,n},create:function(){var a=this.extend();return a.init.apply(a,arguments),a},init:function(){},mixIn:function(a){for(var n in a)a.hasOwnProperty(n)&&(this[n]=a[n]);a.hasOwnProperty("toString")&&(this.toString=a.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),h=r.WordArray=t.extend({init:function(a,n){a=this.words=a||[],n!=C?this.sigBytes=n:this.sigBytes=a.length*4},toString:function(a){return(a||f).stringify(this)},concat:function(a){var n=this.words,i=a.words,p=this.sigBytes,A=a.sigBytes;if(this.clamp(),p%4)for(var D=0;D<A;D++){var _=i[D>>>2]>>>24-D%4*8&255;n[p+D>>>2]|=_<<24-(p+D)%4*8}else for(var P=0;P<A;P+=4)n[p+P>>>2]=i[P>>>2];return this.sigBytes+=A,this},clamp:function(){var a=this.words,n=this.sigBytes;a[n>>>2]&=4294967295<<32-n%4*8,a.length=e.ceil(n/4)},clone:function(){var a=t.clone.call(this);return a.words=this.words.slice(0),a},random:function(a){for(var n=[],i=0;i<a;i+=4)n.push(g());return new h.init(n,a)}}),x=l.enc={},f=x.Hex={stringify:function(a){for(var n=a.words,i=a.sigBytes,p=[],A=0;A<i;A++){var D=n[A>>>2]>>>24-A%4*8&255;p.push((D>>>4).toString(16)),p.push((D&15).toString(16))}return p.join("")},parse:function(a){for(var n=a.length,i=[],p=0;p<n;p+=2)i[p>>>3]|=parseInt(a.substr(p,2),16)<<24-p%8*4;return new h.init(i,n/2)}},o=x.Latin1={stringify:function(a){for(var n=a.words,i=a.sigBytes,p=[],A=0;A<i;A++){var D=n[A>>>2]>>>24-A%4*8&255;p.push(String.fromCharCode(D))}return p.join("")},parse:function(a){for(var n=a.length,i=[],p=0;p<n;p++)i[p>>>2]|=(a.charCodeAt(p)&255)<<24-p%4*8;return new h.init(i,n)}},v=x.Utf8={stringify:function(a){try{return decodeURIComponent(escape(o.stringify(a)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(a){return o.parse(unescape(encodeURIComponent(a)))}},s=r.BufferedBlockAlgorithm=t.extend({reset:function(){this._data=new h.init,this._nDataBytes=0},_append:function(a){typeof a=="string"&&(a=v.parse(a)),this._data.concat(a),this._nDataBytes+=a.sigBytes},_process:function(a){var n,i=this._data,p=i.words,A=i.sigBytes,D=this.blockSize,_=D*4,P=A/_;a?P=e.ceil(P):P=e.max((P|0)-this._minBufferSize,0);var d=P*D,F=e.min(d*4,A);if(d){for(var y=0;y<d;y+=D)this._doProcessBlock(p,y);n=p.splice(0,d),i.sigBytes-=F}return new h.init(n,F)},clone:function(){var a=t.clone.call(this);return a._data=this._data.clone(),a},_minBufferSize:0});r.Hasher=s.extend({cfg:t.extend(),init:function(a){this.cfg=this.cfg.extend(a),this.reset()},reset:function(){s.reset.call(this),this._doReset()},update:function(a){return this._append(a),this._process(),this},finalize:function(a){a&&this._append(a);var n=this._doFinalize();return n},blockSize:16,_createHelper:function(a){return function(n,i){return new a.init(i).finalize(n)}},_createHmacHelper:function(a){return function(n,i){return new B.HMAC.init(a,i).finalize(n)}}});var B=l.algo={};return l}(Math);return c})}(D0)),D0.exports}var F0={exports:{}},sr;function B0(){return sr||(sr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(e){var C=c,E=C.lib,g=E.Base,u=E.WordArray,l=C.x64={};l.Word=g.extend({init:function(r,t){this.high=r,this.low=t}}),l.WordArray=g.extend({init:function(r,t){r=this.words=r||[],t!=e?this.sigBytes=t:this.sigBytes=r.length*8},toX32:function(){for(var r=this.words,t=r.length,h=[],x=0;x<t;x++){var f=r[x];h.push(f.high),h.push(f.low)}return u.create(h,this.sigBytes)},clone:function(){for(var r=g.clone.call(this),t=r.words=this.words.slice(0),h=t.length,x=0;x<h;x++)t[x]=t[x].clone();return r}})}(),c})}(F0)),F0.exports}var _0={exports:{}},fr;function se(){return fr||(fr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(){if(typeof ArrayBuffer=="function"){var e=c,C=e.lib,E=C.WordArray,g=E.init,u=E.init=function(l){if(l instanceof ArrayBuffer&&(l=new Uint8Array(l)),(l instanceof Int8Array||typeof Uint8ClampedArray<"u"&&l instanceof Uint8ClampedArray||l instanceof Int16Array||l instanceof Uint16Array||l instanceof Int32Array||l instanceof Uint32Array||l instanceof Float32Array||l instanceof Float64Array)&&(l=new Uint8Array(l.buffer,l.byteOffset,l.byteLength)),l instanceof Uint8Array){for(var r=l.byteLength,t=[],h=0;h<r;h++)t[h>>>2]|=l[h]<<24-h%4*8;g.call(this,t,r)}else g.apply(this,arguments)};u.prototype=E}}(),c.lib.WordArray})}(_0)),_0.exports}var b0={exports:{}},cr;function fe(){return cr||(cr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=e.enc;g.Utf16=g.Utf16BE={stringify:function(l){for(var r=l.words,t=l.sigBytes,h=[],x=0;x<t;x+=2){var f=r[x>>>2]>>>16-x%4*8&65535;h.push(String.fromCharCode(f))}return h.join("")},parse:function(l){for(var r=l.length,t=[],h=0;h<r;h++)t[h>>>1]|=l.charCodeAt(h)<<16-h%2*16;return E.create(t,r*2)}},g.Utf16LE={stringify:function(l){for(var r=l.words,t=l.sigBytes,h=[],x=0;x<t;x+=2){var f=u(r[x>>>2]>>>16-x%4*8&65535);h.push(String.fromCharCode(f))}return h.join("")},parse:function(l){for(var r=l.length,t=[],h=0;h<r;h++)t[h>>>1]|=u(l.charCodeAt(h)<<16-h%2*16);return E.create(t,r*2)}};function u(l){return l<<8&4278255360|l>>>8&16711935}}(),c.enc.Utf16})}(b0)),b0.exports}var g0={exports:{}},vr;function t0(){return vr||(vr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=e.enc;g.Base64={stringify:function(l){var r=l.words,t=l.sigBytes,h=this._map;l.clamp();for(var x=[],f=0;f<t;f+=3)for(var o=r[f>>>2]>>>24-f%4*8&255,v=r[f+1>>>2]>>>24-(f+1)%4*8&255,s=r[f+2>>>2]>>>24-(f+2)%4*8&255,B=o<<16|v<<8|s,a=0;a<4&&f+a*.75<t;a++)x.push(h.charAt(B>>>6*(3-a)&63));var n=h.charAt(64);if(n)for(;x.length%4;)x.push(n);return x.join("")},parse:function(l){var r=l.length,t=this._map,h=this._reverseMap;if(!h){h=this._reverseMap=[];for(var x=0;x<t.length;x++)h[t.charCodeAt(x)]=x}var f=t.charAt(64);if(f){var o=l.indexOf(f);o!==-1&&(r=o)}return u(l,r,h)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function u(l,r,t){for(var h=[],x=0,f=0;f<r;f++)if(f%4){var o=t[l.charCodeAt(f-1)]<<f%4*2,v=t[l.charCodeAt(f)]>>>6-f%4*2,s=o|v;h[x>>>2]|=s<<24-x%4*8,x++}return E.create(h,x)}}(),c.enc.Base64})}(g0)),g0.exports}var y0={exports:{}},ur;function ce(){return ur||(ur=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=e.enc;g.Base64url={stringify:function(l,r){r===void 0&&(r=!0);var t=l.words,h=l.sigBytes,x=r?this._safe_map:this._map;l.clamp();for(var f=[],o=0;o<h;o+=3)for(var v=t[o>>>2]>>>24-o%4*8&255,s=t[o+1>>>2]>>>24-(o+1)%4*8&255,B=t[o+2>>>2]>>>24-(o+2)%4*8&255,a=v<<16|s<<8|B,n=0;n<4&&o+n*.75<h;n++)f.push(x.charAt(a>>>6*(3-n)&63));var i=x.charAt(64);if(i)for(;f.length%4;)f.push(i);return f.join("")},parse:function(l,r){r===void 0&&(r=!0);var t=l.length,h=r?this._safe_map:this._map,x=this._reverseMap;if(!x){x=this._reverseMap=[];for(var f=0;f<h.length;f++)x[h.charCodeAt(f)]=f}var o=h.charAt(64);if(o){var v=l.indexOf(o);v!==-1&&(t=v)}return u(l,t,x)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function u(l,r,t){for(var h=[],x=0,f=0;f<r;f++)if(f%4){var o=t[l.charCodeAt(f-1)]<<f%4*2,v=t[l.charCodeAt(f)]>>>6-f%4*2,s=o|v;h[x>>>2]|=s<<24-x%4*8,x++}return E.create(h,x)}}(),c.enc.Base64url})}(y0)),y0.exports}var k0={exports:{}},dr;function a0(){return dr||(dr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(e){var C=c,E=C.lib,g=E.WordArray,u=E.Hasher,l=C.algo,r=[];(function(){for(var v=0;v<64;v++)r[v]=e.abs(e.sin(v+1))*4294967296|0})();var t=l.MD5=u.extend({_doReset:function(){this._hash=new g.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(v,s){for(var B=0;B<16;B++){var a=s+B,n=v[a];v[a]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360}var i=this._hash.words,p=v[s+0],A=v[s+1],D=v[s+2],_=v[s+3],P=v[s+4],d=v[s+5],F=v[s+6],y=v[s+7],k=v[s+8],z=v[s+9],W=v[s+10],T=v[s+11],K=v[s+12],O=v[s+13],N=v[s+14],U=v[s+15],b=i[0],S=i[1],m=i[2],w=i[3];b=h(b,S,m,w,p,7,r[0]),w=h(w,b,S,m,A,12,r[1]),m=h(m,w,b,S,D,17,r[2]),S=h(S,m,w,b,_,22,r[3]),b=h(b,S,m,w,P,7,r[4]),w=h(w,b,S,m,d,12,r[5]),m=h(m,w,b,S,F,17,r[6]),S=h(S,m,w,b,y,22,r[7]),b=h(b,S,m,w,k,7,r[8]),w=h(w,b,S,m,z,12,r[9]),m=h(m,w,b,S,W,17,r[10]),S=h(S,m,w,b,T,22,r[11]),b=h(b,S,m,w,K,7,r[12]),w=h(w,b,S,m,O,12,r[13]),m=h(m,w,b,S,N,17,r[14]),S=h(S,m,w,b,U,22,r[15]),b=x(b,S,m,w,A,5,r[16]),w=x(w,b,S,m,F,9,r[17]),m=x(m,w,b,S,T,14,r[18]),S=x(S,m,w,b,p,20,r[19]),b=x(b,S,m,w,d,5,r[20]),w=x(w,b,S,m,W,9,r[21]),m=x(m,w,b,S,U,14,r[22]),S=x(S,m,w,b,P,20,r[23]),b=x(b,S,m,w,z,5,r[24]),w=x(w,b,S,m,N,9,r[25]),m=x(m,w,b,S,_,14,r[26]),S=x(S,m,w,b,k,20,r[27]),b=x(b,S,m,w,O,5,r[28]),w=x(w,b,S,m,D,9,r[29]),m=x(m,w,b,S,y,14,r[30]),S=x(S,m,w,b,K,20,r[31]),b=f(b,S,m,w,d,4,r[32]),w=f(w,b,S,m,k,11,r[33]),m=f(m,w,b,S,T,16,r[34]),S=f(S,m,w,b,N,23,r[35]),b=f(b,S,m,w,A,4,r[36]),w=f(w,b,S,m,P,11,r[37]),m=f(m,w,b,S,y,16,r[38]),S=f(S,m,w,b,W,23,r[39]),b=f(b,S,m,w,O,4,r[40]),w=f(w,b,S,m,p,11,r[41]),m=f(m,w,b,S,_,16,r[42]),S=f(S,m,w,b,F,23,r[43]),b=f(b,S,m,w,z,4,r[44]),w=f(w,b,S,m,K,11,r[45]),m=f(m,w,b,S,U,16,r[46]),S=f(S,m,w,b,D,23,r[47]),b=o(b,S,m,w,p,6,r[48]),w=o(w,b,S,m,y,10,r[49]),m=o(m,w,b,S,N,15,r[50]),S=o(S,m,w,b,d,21,r[51]),b=o(b,S,m,w,K,6,r[52]),w=o(w,b,S,m,_,10,r[53]),m=o(m,w,b,S,W,15,r[54]),S=o(S,m,w,b,A,21,r[55]),b=o(b,S,m,w,k,6,r[56]),w=o(w,b,S,m,U,10,r[57]),m=o(m,w,b,S,F,15,r[58]),S=o(S,m,w,b,O,21,r[59]),b=o(b,S,m,w,P,6,r[60]),w=o(w,b,S,m,T,10,r[61]),m=o(m,w,b,S,D,15,r[62]),S=o(S,m,w,b,z,21,r[63]),i[0]=i[0]+b|0,i[1]=i[1]+S|0,i[2]=i[2]+m|0,i[3]=i[3]+w|0},_doFinalize:function(){var v=this._data,s=v.words,B=this._nDataBytes*8,a=v.sigBytes*8;s[a>>>5]|=128<<24-a%32;var n=e.floor(B/4294967296),i=B;s[(a+64>>>9<<4)+15]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,s[(a+64>>>9<<4)+14]=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,v.sigBytes=(s.length+1)*4,this._process();for(var p=this._hash,A=p.words,D=0;D<4;D++){var _=A[D];A[D]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}return p},clone:function(){var v=u.clone.call(this);return v._hash=this._hash.clone(),v}});function h(v,s,B,a,n,i,p){var A=v+(s&B|~s&a)+n+p;return(A<<i|A>>>32-i)+s}function x(v,s,B,a,n,i,p){var A=v+(s&a|B&~a)+n+p;return(A<<i|A>>>32-i)+s}function f(v,s,B,a,n,i,p){var A=v+(s^B^a)+n+p;return(A<<i|A>>>32-i)+s}function o(v,s,B,a,n,i,p){var A=v+(B^(s|~a))+n+p;return(A<<i|A>>>32-i)+s}C.MD5=u._createHelper(t),C.HmacMD5=u._createHmacHelper(t)}(Math),c.MD5})}(k0)),k0.exports}var w0={exports:{}},hr;function lr(){return hr||(hr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=C.Hasher,u=e.algo,l=[],r=u.SHA1=g.extend({_doReset:function(){this._hash=new E.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,h){for(var x=this._hash.words,f=x[0],o=x[1],v=x[2],s=x[3],B=x[4],a=0;a<80;a++){if(a<16)l[a]=t[h+a]|0;else{var n=l[a-3]^l[a-8]^l[a-14]^l[a-16];l[a]=n<<1|n>>>31}var i=(f<<5|f>>>27)+B+l[a];a<20?i+=(o&v|~o&s)+1518500249:a<40?i+=(o^v^s)+1859775393:a<60?i+=(o&v|o&s|v&s)-1894007588:i+=(o^v^s)-899497514,B=s,s=v,v=o<<30|o>>>2,o=f,f=i}x[0]=x[0]+f|0,x[1]=x[1]+o|0,x[2]=x[2]+v|0,x[3]=x[3]+s|0,x[4]=x[4]+B|0},_doFinalize:function(){var t=this._data,h=t.words,x=this._nDataBytes*8,f=t.sigBytes*8;return h[f>>>5]|=128<<24-f%32,h[(f+64>>>9<<4)+14]=Math.floor(x/4294967296),h[(f+64>>>9<<4)+15]=x,t.sigBytes=h.length*4,this._process(),this._hash},clone:function(){var t=g.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA1=g._createHelper(r),e.HmacSHA1=g._createHmacHelper(r)}(),c.SHA1})}(w0)),w0.exports}var S0={exports:{}},Br;function m0(){return Br||(Br=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){return function(e){var C=c,E=C.lib,g=E.WordArray,u=E.Hasher,l=C.algo,r=[],t=[];(function(){function f(B){for(var a=e.sqrt(B),n=2;n<=a;n++)if(!(B%n))return!1;return!0}function o(B){return(B-(B|0))*4294967296|0}for(var v=2,s=0;s<64;)f(v)&&(s<8&&(r[s]=o(e.pow(v,1/2))),t[s]=o(e.pow(v,1/3)),s++),v++})();var h=[],x=l.SHA256=u.extend({_doReset:function(){this._hash=new g.init(r.slice(0))},_doProcessBlock:function(f,o){for(var v=this._hash.words,s=v[0],B=v[1],a=v[2],n=v[3],i=v[4],p=v[5],A=v[6],D=v[7],_=0;_<64;_++){if(_<16)h[_]=f[o+_]|0;else{var P=h[_-15],d=(P<<25|P>>>7)^(P<<14|P>>>18)^P>>>3,F=h[_-2],y=(F<<15|F>>>17)^(F<<13|F>>>19)^F>>>10;h[_]=d+h[_-7]+y+h[_-16]}var k=i&p^~i&A,z=s&B^s&a^B&a,W=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),T=(i<<26|i>>>6)^(i<<21|i>>>11)^(i<<7|i>>>25),K=D+T+k+t[_]+h[_],O=W+z;D=A,A=p,p=i,i=n+K|0,n=a,a=B,B=s,s=K+O|0}v[0]=v[0]+s|0,v[1]=v[1]+B|0,v[2]=v[2]+a|0,v[3]=v[3]+n|0,v[4]=v[4]+i|0,v[5]=v[5]+p|0,v[6]=v[6]+A|0,v[7]=v[7]+D|0},_doFinalize:function(){var f=this._data,o=f.words,v=this._nDataBytes*8,s=f.sigBytes*8;return o[s>>>5]|=128<<24-s%32,o[(s+64>>>9<<4)+14]=e.floor(v/4294967296),o[(s+64>>>9<<4)+15]=v,f.sigBytes=o.length*4,this._process(),this._hash},clone:function(){var f=u.clone.call(this);return f._hash=this._hash.clone(),f}});C.SHA256=u._createHelper(x),C.HmacSHA256=u._createHmacHelper(x)}(Math),c.SHA256})}(S0)),S0.exports}var H0={exports:{}},Cr;function ve(){return Cr||(Cr=1,function(H,R){(function(c,e,C){H.exports=e(L(),m0())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=e.algo,u=g.SHA256,l=g.SHA224=u.extend({_doReset:function(){this._hash=new E.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var r=u._doFinalize.call(this);return r.sigBytes-=4,r}});e.SHA224=u._createHelper(l),e.HmacSHA224=u._createHmacHelper(l)}(),c.SHA224})}(H0)),H0.exports}var R0={exports:{}},Ar;function Er(){return Ar||(Ar=1,function(H,R){(function(c,e,C){H.exports=e(L(),B0())})(q,function(c){return function(){var e=c,C=e.lib,E=C.Hasher,g=e.x64,u=g.Word,l=g.WordArray,r=e.algo;function t(){return u.create.apply(u,arguments)}var h=[t(1116352408,3609767458),t(1899447441,602891725),t(3049323471,3964484399),t(3921009573,2173295548),t(961987163,4081628472),t(1508970993,3053834265),t(2453635748,2937671579),t(2870763221,3664609560),t(3624381080,2734883394),t(310598401,1164996542),t(607225278,1323610764),t(1426881987,3590304994),t(1925078388,4068182383),t(2162078206,991336113),t(2614888103,633803317),t(3248222580,3479774868),t(3835390401,2666613458),t(4022224774,944711139),t(264347078,2341262773),t(604807628,2007800933),t(770255983,1495990901),t(1249150122,1856431235),t(1555081692,3175218132),t(1996064986,2198950837),t(2554220882,3999719339),t(2821834349,766784016),t(2952996808,2566594879),t(3210313671,3203337956),t(3336571891,1034457026),t(3584528711,2466948901),t(113926993,3758326383),t(338241895,168717936),t(666307205,1188179964),t(773529912,1546045734),t(1294757372,1522805485),t(1396182291,2643833823),t(1695183700,2343527390),t(1986661051,1014477480),t(2177026350,1206759142),t(2456956037,344077627),t(2730485921,1290863460),t(2820302411,3158454273),t(3259730800,3505952657),t(3345764771,106217008),t(3516065817,3606008344),t(3600352804,1432725776),t(4094571909,1467031594),t(275423344,851169720),t(430227734,3100823752),t(506948616,1363258195),t(659060556,3750685593),t(883997877,3785050280),t(958139571,3318307427),t(1322822218,3812723403),t(1537002063,2003034995),t(1747873779,3602036899),t(1955562222,1575990012),t(2024104815,1125592928),t(2227730452,2716904306),t(2361852424,442776044),t(2428436474,593698344),t(2756734187,3733110249),t(3204031479,2999351573),t(3329325298,3815920427),t(3391569614,3928383900),t(3515267271,566280711),t(3940187606,3454069534),t(4118630271,4000239992),t(116418474,1914138554),t(174292421,2731055270),t(289380356,3203993006),t(460393269,320620315),t(685471733,587496836),t(852142971,1086792851),t(1017036298,365543100),t(1126000580,2618297676),t(1288033470,3409855158),t(1501505948,4234509866),t(1607167915,987167468),t(1816402316,1246189591)],x=[];(function(){for(var o=0;o<80;o++)x[o]=t()})();var f=r.SHA512=E.extend({_doReset:function(){this._hash=new l.init([new u.init(1779033703,4089235720),new u.init(3144134277,2227873595),new u.init(1013904242,4271175723),new u.init(2773480762,1595750129),new u.init(1359893119,2917565137),new u.init(2600822924,725511199),new u.init(528734635,4215389547),new u.init(1541459225,327033209)])},_doProcessBlock:function(o,v){for(var s=this._hash.words,B=s[0],a=s[1],n=s[2],i=s[3],p=s[4],A=s[5],D=s[6],_=s[7],P=B.high,d=B.low,F=a.high,y=a.low,k=n.high,z=n.low,W=i.high,T=i.low,K=p.high,O=p.low,N=A.high,U=A.low,b=D.high,S=D.low,m=_.high,w=_.low,X=P,M=d,$=F,I=y,f0=k,n0=z,ar=W,c0=T,Y=K,j=O,A0=N,v0=U,E0=b,u0=S,nr=m,d0=w,V=0;V<80;V++){var Q,e0,p0=x[V];if(V<16)e0=p0.high=o[v+V*2]|0,Q=p0.low=o[v+V*2+1]|0;else{var Gr=x[V-15],o0=Gr.high,h0=Gr.low,qe=(o0>>>1|h0<<31)^(o0>>>8|h0<<24)^o0>>>7,$r=(h0>>>1|o0<<31)^(h0>>>8|o0<<24)^(h0>>>7|o0<<25),jr=x[V-2],i0=jr.high,l0=jr.low,Te=(i0>>>19|l0<<13)^(i0<<3|l0>>>29)^i0>>>6,Zr=(l0>>>19|i0<<13)^(l0<<3|i0>>>29)^(l0>>>6|i0<<26),Qr=x[V-7],Ie=Qr.high,Le=Qr.low,Yr=x[V-16],Oe=Yr.high,Vr=Yr.low;Q=$r+Le,e0=qe+Ie+(Q>>>0<$r>>>0?1:0),Q=Q+Zr,e0=e0+Te+(Q>>>0<Zr>>>0?1:0),Q=Q+Vr,e0=e0+Oe+(Q>>>0<Vr>>>0?1:0),p0.high=e0,p0.low=Q}var Ue=Y&A0^~Y&E0,Jr=j&v0^~j&u0,Ne=X&$^X&f0^$&f0,Me=M&I^M&n0^I&n0,Ke=(X>>>28|M<<4)^(X<<30|M>>>2)^(X<<25|M>>>7),re=(M>>>28|X<<4)^(M<<30|X>>>2)^(M<<25|X>>>7),Xe=(Y>>>14|j<<18)^(Y>>>18|j<<14)^(Y<<23|j>>>9),Ge=(j>>>14|Y<<18)^(j>>>18|Y<<14)^(j<<23|Y>>>9),ee=h[V],$e=ee.high,xe=ee.low,Z=d0+Ge,x0=nr+Xe+(Z>>>0<d0>>>0?1:0),Z=Z+Jr,x0=x0+Ue+(Z>>>0<Jr>>>0?1:0),Z=Z+xe,x0=x0+$e+(Z>>>0<xe>>>0?1:0),Z=Z+Q,x0=x0+e0+(Z>>>0<Q>>>0?1:0),te=re+Me,je=Ke+Ne+(te>>>0<re>>>0?1:0);nr=E0,d0=u0,E0=A0,u0=v0,A0=Y,v0=j,j=c0+Z|0,Y=ar+x0+(j>>>0<c0>>>0?1:0)|0,ar=f0,c0=n0,f0=$,n0=I,$=X,I=M,M=Z+te|0,X=x0+je+(M>>>0<Z>>>0?1:0)|0}d=B.low=d+M,B.high=P+X+(d>>>0<M>>>0?1:0),y=a.low=y+I,a.high=F+$+(y>>>0<I>>>0?1:0),z=n.low=z+n0,n.high=k+f0+(z>>>0<n0>>>0?1:0),T=i.low=T+c0,i.high=W+ar+(T>>>0<c0>>>0?1:0),O=p.low=O+j,p.high=K+Y+(O>>>0<j>>>0?1:0),U=A.low=U+v0,A.high=N+A0+(U>>>0<v0>>>0?1:0),S=D.low=S+u0,D.high=b+E0+(S>>>0<u0>>>0?1:0),w=_.low=w+d0,_.high=m+nr+(w>>>0<d0>>>0?1:0)},_doFinalize:function(){var o=this._data,v=o.words,s=this._nDataBytes*8,B=o.sigBytes*8;v[B>>>5]|=128<<24-B%32,v[(B+128>>>10<<5)+30]=Math.floor(s/4294967296),v[(B+128>>>10<<5)+31]=s,o.sigBytes=v.length*4,this._process();var a=this._hash.toX32();return a},clone:function(){var o=E.clone.call(this);return o._hash=this._hash.clone(),o},blockSize:1024/32});e.SHA512=E._createHelper(f),e.HmacSHA512=E._createHmacHelper(f)}(),c.SHA512})}(R0)),R0.exports}var P0={exports:{}},pr;function ue(){return pr||(pr=1,function(H,R){(function(c,e,C){H.exports=e(L(),B0(),Er())})(q,function(c){return function(){var e=c,C=e.x64,E=C.Word,g=C.WordArray,u=e.algo,l=u.SHA512,r=u.SHA384=l.extend({_doReset:function(){this._hash=new g.init([new E.init(3418070365,3238371032),new E.init(1654270250,914150663),new E.init(2438529370,812702999),new E.init(355462360,4144912697),new E.init(1731405415,4290775857),new E.init(2394180231,1750603025),new E.init(3675008525,1694076839),new E.init(1203062813,3204075428)])},_doFinalize:function(){var t=l._doFinalize.call(this);return t.sigBytes-=16,t}});e.SHA384=l._createHelper(r),e.HmacSHA384=l._createHmacHelper(r)}(),c.SHA384})}(P0)),P0.exports}var z0={exports:{}},Dr;function de(){return Dr||(Dr=1,function(H,R){(function(c,e,C){H.exports=e(L(),B0())})(q,function(c){return function(e){var C=c,E=C.lib,g=E.WordArray,u=E.Hasher,l=C.x64,r=l.Word,t=C.algo,h=[],x=[],f=[];(function(){for(var s=1,B=0,a=0;a<24;a++){h[s+5*B]=(a+1)*(a+2)/2%64;var n=B%5,i=(2*s+3*B)%5;s=n,B=i}for(var s=0;s<5;s++)for(var B=0;B<5;B++)x[s+5*B]=B+(2*s+3*B)%5*5;for(var p=1,A=0;A<24;A++){for(var D=0,_=0,P=0;P<7;P++){if(p&1){var d=(1<<P)-1;d<32?_^=1<<d:D^=1<<d-32}p&128?p=p<<1^113:p<<=1}f[A]=r.create(D,_)}})();var o=[];(function(){for(var s=0;s<25;s++)o[s]=r.create()})();var v=t.SHA3=u.extend({cfg:u.cfg.extend({outputLength:512}),_doReset:function(){for(var s=this._state=[],B=0;B<25;B++)s[B]=new r.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(s,B){for(var a=this._state,n=this.blockSize/2,i=0;i<n;i++){var p=s[B+2*i],A=s[B+2*i+1];p=(p<<8|p>>>24)&16711935|(p<<24|p>>>8)&4278255360,A=(A<<8|A>>>24)&16711935|(A<<24|A>>>8)&4278255360;var D=a[i];D.high^=A,D.low^=p}for(var _=0;_<24;_++){for(var P=0;P<5;P++){for(var d=0,F=0,y=0;y<5;y++){var D=a[P+5*y];d^=D.high,F^=D.low}var k=o[P];k.high=d,k.low=F}for(var P=0;P<5;P++)for(var z=o[(P+4)%5],W=o[(P+1)%5],T=W.high,K=W.low,d=z.high^(T<<1|K>>>31),F=z.low^(K<<1|T>>>31),y=0;y<5;y++){var D=a[P+5*y];D.high^=d,D.low^=F}for(var O=1;O<25;O++){var d,F,D=a[O],N=D.high,U=D.low,b=h[O];b<32?(d=N<<b|U>>>32-b,F=U<<b|N>>>32-b):(d=U<<b-32|N>>>64-b,F=N<<b-32|U>>>64-b);var S=o[x[O]];S.high=d,S.low=F}var m=o[0],w=a[0];m.high=w.high,m.low=w.low;for(var P=0;P<5;P++)for(var y=0;y<5;y++){var O=P+5*y,D=a[O],X=o[O],M=o[(P+1)%5+5*y],$=o[(P+2)%5+5*y];D.high=X.high^~M.high&$.high,D.low=X.low^~M.low&$.low}var D=a[0],I=f[_];D.high^=I.high,D.low^=I.low}},_doFinalize:function(){var s=this._data,B=s.words;this._nDataBytes*8;var a=s.sigBytes*8,n=this.blockSize*32;B[a>>>5]|=1<<24-a%32,B[(e.ceil((a+1)/n)*n>>>5)-1]|=128,s.sigBytes=B.length*4,this._process();for(var i=this._state,p=this.cfg.outputLength/8,A=p/8,D=[],_=0;_<A;_++){var P=i[_],d=P.high,F=P.low;d=(d<<8|d>>>24)&16711935|(d<<24|d>>>8)&4278255360,F=(F<<8|F>>>24)&16711935|(F<<24|F>>>8)&4278255360,D.push(F),D.push(d)}return new g.init(D,p)},clone:function(){for(var s=u.clone.call(this),B=s._state=this._state.slice(0),a=0;a<25;a++)B[a]=B[a].clone();return s}});C.SHA3=u._createHelper(v),C.HmacSHA3=u._createHmacHelper(v)}(Math),c.SHA3})}(z0)),z0.exports}var W0={exports:{}},Fr;function he(){return Fr||(Fr=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){/** @preserve
				(c) 2012 by Cédric Mesnil. All rights reserved.

				Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

				    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
				    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

				THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
				*/return function(e){var C=c,E=C.lib,g=E.WordArray,u=E.Hasher,l=C.algo,r=g.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),t=g.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),h=g.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),x=g.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),f=g.create([0,1518500249,1859775393,2400959708,2840853838]),o=g.create([1352829926,1548603684,1836072691,2053994217,0]),v=l.RIPEMD160=u.extend({_doReset:function(){this._hash=g.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(A,D){for(var _=0;_<16;_++){var P=D+_,d=A[P];A[P]=(d<<8|d>>>24)&16711935|(d<<24|d>>>8)&4278255360}var F=this._hash.words,y=f.words,k=o.words,z=r.words,W=t.words,T=h.words,K=x.words,O,N,U,b,S,m,w,X,M,$;m=O=F[0],w=N=F[1],X=U=F[2],M=b=F[3],$=S=F[4];for(var I,_=0;_<80;_+=1)I=O+A[D+z[_]]|0,_<16?I+=s(N,U,b)+y[0]:_<32?I+=B(N,U,b)+y[1]:_<48?I+=a(N,U,b)+y[2]:_<64?I+=n(N,U,b)+y[3]:I+=i(N,U,b)+y[4],I=I|0,I=p(I,T[_]),I=I+S|0,O=S,S=b,b=p(U,10),U=N,N=I,I=m+A[D+W[_]]|0,_<16?I+=i(w,X,M)+k[0]:_<32?I+=n(w,X,M)+k[1]:_<48?I+=a(w,X,M)+k[2]:_<64?I+=B(w,X,M)+k[3]:I+=s(w,X,M)+k[4],I=I|0,I=p(I,K[_]),I=I+$|0,m=$,$=M,M=p(X,10),X=w,w=I;I=F[1]+U+M|0,F[1]=F[2]+b+$|0,F[2]=F[3]+S+m|0,F[3]=F[4]+O+w|0,F[4]=F[0]+N+X|0,F[0]=I},_doFinalize:function(){var A=this._data,D=A.words,_=this._nDataBytes*8,P=A.sigBytes*8;D[P>>>5]|=128<<24-P%32,D[(P+64>>>9<<4)+14]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360,A.sigBytes=(D.length+1)*4,this._process();for(var d=this._hash,F=d.words,y=0;y<5;y++){var k=F[y];F[y]=(k<<8|k>>>24)&16711935|(k<<24|k>>>8)&4278255360}return d},clone:function(){var A=u.clone.call(this);return A._hash=this._hash.clone(),A}});function s(A,D,_){return A^D^_}function B(A,D,_){return A&D|~A&_}function a(A,D,_){return(A|~D)^_}function n(A,D,_){return A&_|D&~_}function i(A,D,_){return A^(D|~_)}function p(A,D){return A<<D|A>>>32-D}C.RIPEMD160=u._createHelper(v),C.HmacRIPEMD160=u._createHmacHelper(v)}(),c.RIPEMD160})}(W0)),W0.exports}var q0={exports:{}},_r;function T0(){return _r||(_r=1,function(H,R){(function(c,e){H.exports=e(L())})(q,function(c){(function(){var e=c,C=e.lib,E=C.Base,g=e.enc,u=g.Utf8,l=e.algo;l.HMAC=E.extend({init:function(r,t){r=this._hasher=new r.init,typeof t=="string"&&(t=u.parse(t));var h=r.blockSize,x=h*4;t.sigBytes>x&&(t=r.finalize(t)),t.clamp();for(var f=this._oKey=t.clone(),o=this._iKey=t.clone(),v=f.words,s=o.words,B=0;B<h;B++)v[B]^=1549556828,s[B]^=909522486;f.sigBytes=o.sigBytes=x,this.reset()},reset:function(){var r=this._hasher;r.reset(),r.update(this._iKey)},update:function(r){return this._hasher.update(r),this},finalize:function(r){var t=this._hasher,h=t.finalize(r);t.reset();var x=t.finalize(this._oKey.clone().concat(h));return x}})})()})}(q0)),q0.exports}var I0={exports:{}},br;function le(){return br||(br=1,function(H,R){(function(c,e,C){H.exports=e(L(),m0(),T0())})(q,function(c){return function(){var e=c,C=e.lib,E=C.Base,g=C.WordArray,u=e.algo,l=u.SHA256,r=u.HMAC,t=u.PBKDF2=E.extend({cfg:E.extend({keySize:128/32,hasher:l,iterations:25e4}),init:function(h){this.cfg=this.cfg.extend(h)},compute:function(h,x){for(var f=this.cfg,o=r.create(f.hasher,h),v=g.create(),s=g.create([1]),B=v.words,a=s.words,n=f.keySize,i=f.iterations;B.length<n;){var p=o.update(x).finalize(s);o.reset();for(var A=p.words,D=A.length,_=p,P=1;P<i;P++){_=o.finalize(_),o.reset();for(var d=_.words,F=0;F<D;F++)A[F]^=d[F]}v.concat(p),a[0]++}return v.sigBytes=n*4,v}});e.PBKDF2=function(h,x,f){return t.create(f).compute(h,x)}}(),c.PBKDF2})}(I0)),I0.exports}var L0={exports:{}},gr;function r0(){return gr||(gr=1,function(H,R){(function(c,e,C){H.exports=e(L(),lr(),T0())})(q,function(c){return function(){var e=c,C=e.lib,E=C.Base,g=C.WordArray,u=e.algo,l=u.MD5,r=u.EvpKDF=E.extend({cfg:E.extend({keySize:128/32,hasher:l,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,h){for(var x,f=this.cfg,o=f.hasher.create(),v=g.create(),s=v.words,B=f.keySize,a=f.iterations;s.length<B;){x&&o.update(x),x=o.update(t).finalize(h),o.reset();for(var n=1;n<a;n++)x=o.finalize(x),o.reset();v.concat(x)}return v.sigBytes=B*4,v}});e.EvpKDF=function(t,h,x){return r.create(x).compute(t,h)}}(),c.EvpKDF})}(L0)),L0.exports}var O0={exports:{}},yr;function G(){return yr||(yr=1,function(H,R){(function(c,e,C){H.exports=e(L(),r0())})(q,function(c){c.lib.Cipher||function(e){var C=c,E=C.lib,g=E.Base,u=E.WordArray,l=E.BufferedBlockAlgorithm,r=C.enc;r.Utf8;var t=r.Base64,h=C.algo,x=h.EvpKDF,f=E.Cipher=l.extend({cfg:g.extend(),createEncryptor:function(d,F){return this.create(this._ENC_XFORM_MODE,d,F)},createDecryptor:function(d,F){return this.create(this._DEC_XFORM_MODE,d,F)},init:function(d,F,y){this.cfg=this.cfg.extend(y),this._xformMode=d,this._key=F,this.reset()},reset:function(){l.reset.call(this),this._doReset()},process:function(d){return this._append(d),this._process()},finalize:function(d){d&&this._append(d);var F=this._doFinalize();return F},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function d(F){return typeof F=="string"?P:A}return function(F){return{encrypt:function(y,k,z){return d(k).encrypt(F,y,k,z)},decrypt:function(y,k,z){return d(k).decrypt(F,y,k,z)}}}}()});E.StreamCipher=f.extend({_doFinalize:function(){var d=this._process(!0);return d},blockSize:1});var o=C.mode={},v=E.BlockCipherMode=g.extend({createEncryptor:function(d,F){return this.Encryptor.create(d,F)},createDecryptor:function(d,F){return this.Decryptor.create(d,F)},init:function(d,F){this._cipher=d,this._iv=F}}),s=o.CBC=function(){var d=v.extend();d.Encryptor=d.extend({processBlock:function(y,k){var z=this._cipher,W=z.blockSize;F.call(this,y,k,W),z.encryptBlock(y,k),this._prevBlock=y.slice(k,k+W)}}),d.Decryptor=d.extend({processBlock:function(y,k){var z=this._cipher,W=z.blockSize,T=y.slice(k,k+W);z.decryptBlock(y,k),F.call(this,y,k,W),this._prevBlock=T}});function F(y,k,z){var W,T=this._iv;T?(W=T,this._iv=e):W=this._prevBlock;for(var K=0;K<z;K++)y[k+K]^=W[K]}return d}(),B=C.pad={},a=B.Pkcs7={pad:function(d,F){for(var y=F*4,k=y-d.sigBytes%y,z=k<<24|k<<16|k<<8|k,W=[],T=0;T<k;T+=4)W.push(z);var K=u.create(W,k);d.concat(K)},unpad:function(d){var F=d.words[d.sigBytes-1>>>2]&255;d.sigBytes-=F}};E.BlockCipher=f.extend({cfg:f.cfg.extend({mode:s,padding:a}),reset:function(){var d;f.reset.call(this);var F=this.cfg,y=F.iv,k=F.mode;this._xformMode==this._ENC_XFORM_MODE?d=k.createEncryptor:(d=k.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==d?this._mode.init(this,y&&y.words):(this._mode=d.call(k,this,y&&y.words),this._mode.__creator=d)},_doProcessBlock:function(d,F){this._mode.processBlock(d,F)},_doFinalize:function(){var d,F=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(F.pad(this._data,this.blockSize),d=this._process(!0)):(d=this._process(!0),F.unpad(d)),d},blockSize:128/32});var n=E.CipherParams=g.extend({init:function(d){this.mixIn(d)},toString:function(d){return(d||this.formatter).stringify(this)}}),i=C.format={},p=i.OpenSSL={stringify:function(d){var F,y=d.ciphertext,k=d.salt;return k?F=u.create([1398893684,1701076831]).concat(k).concat(y):F=y,F.toString(t)},parse:function(d){var F,y=t.parse(d),k=y.words;return k[0]==1398893684&&k[1]==1701076831&&(F=u.create(k.slice(2,4)),k.splice(0,4),y.sigBytes-=16),n.create({ciphertext:y,salt:F})}},A=E.SerializableCipher=g.extend({cfg:g.extend({format:p}),encrypt:function(d,F,y,k){k=this.cfg.extend(k);var z=d.createEncryptor(y,k),W=z.finalize(F),T=z.cfg;return n.create({ciphertext:W,key:y,iv:T.iv,algorithm:d,mode:T.mode,padding:T.padding,blockSize:d.blockSize,formatter:k.format})},decrypt:function(d,F,y,k){k=this.cfg.extend(k),F=this._parse(F,k.format);var z=d.createDecryptor(y,k).finalize(F.ciphertext);return z},_parse:function(d,F){return typeof d=="string"?F.parse(d,this):d}}),D=C.kdf={},_=D.OpenSSL={execute:function(d,F,y,k,z){if(k||(k=u.random(64/8)),z)var W=x.create({keySize:F+y,hasher:z}).compute(d,k);else var W=x.create({keySize:F+y}).compute(d,k);var T=u.create(W.words.slice(F),y*4);return W.sigBytes=F*4,n.create({key:W,iv:T,salt:k})}},P=E.PasswordBasedCipher=A.extend({cfg:A.cfg.extend({kdf:_}),encrypt:function(d,F,y,k){k=this.cfg.extend(k);var z=k.kdf.execute(y,d.keySize,d.ivSize,k.salt,k.hasher);k.iv=z.iv;var W=A.encrypt.call(this,d,F,z.key,k);return W.mixIn(z),W},decrypt:function(d,F,y,k){k=this.cfg.extend(k),F=this._parse(F,k.format);var z=k.kdf.execute(y,d.keySize,d.ivSize,F.salt,k.hasher);k.iv=z.iv;var W=A.decrypt.call(this,d,F,z.key,k);return W}})}()})}(O0)),O0.exports}var U0={exports:{}},kr;function Be(){return kr||(kr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.mode.CFB=function(){var e=c.lib.BlockCipherMode.extend();e.Encryptor=e.extend({processBlock:function(E,g){var u=this._cipher,l=u.blockSize;C.call(this,E,g,l,u),this._prevBlock=E.slice(g,g+l)}}),e.Decryptor=e.extend({processBlock:function(E,g){var u=this._cipher,l=u.blockSize,r=E.slice(g,g+l);C.call(this,E,g,l,u),this._prevBlock=r}});function C(E,g,u,l){var r,t=this._iv;t?(r=t.slice(0),this._iv=void 0):r=this._prevBlock,l.encryptBlock(r,0);for(var h=0;h<u;h++)E[g+h]^=r[h]}return e}(),c.mode.CFB})}(U0)),U0.exports}var N0={exports:{}},wr;function Ce(){return wr||(wr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.mode.CTR=function(){var e=c.lib.BlockCipherMode.extend(),C=e.Encryptor=e.extend({processBlock:function(E,g){var u=this._cipher,l=u.blockSize,r=this._iv,t=this._counter;r&&(t=this._counter=r.slice(0),this._iv=void 0);var h=t.slice(0);u.encryptBlock(h,0),t[l-1]=t[l-1]+1|0;for(var x=0;x<l;x++)E[g+x]^=h[x]}});return e.Decryptor=C,e}(),c.mode.CTR})}(N0)),N0.exports}var M0={exports:{}},Sr;function Ae(){return Sr||(Sr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return c.mode.CTRGladman=function(){var e=c.lib.BlockCipherMode.extend();function C(u){if((u>>24&255)===255){var l=u>>16&255,r=u>>8&255,t=u&255;l===255?(l=0,r===255?(r=0,t===255?t=0:++t):++r):++l,u=0,u+=l<<16,u+=r<<8,u+=t}else u+=1<<24;return u}function E(u){return(u[0]=C(u[0]))===0&&(u[1]=C(u[1])),u}var g=e.Encryptor=e.extend({processBlock:function(u,l){var r=this._cipher,t=r.blockSize,h=this._iv,x=this._counter;h&&(x=this._counter=h.slice(0),this._iv=void 0),E(x);var f=x.slice(0);r.encryptBlock(f,0);for(var o=0;o<t;o++)u[l+o]^=f[o]}});return e.Decryptor=g,e}(),c.mode.CTRGladman})}(M0)),M0.exports}var K0={exports:{}},mr;function Ee(){return mr||(mr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.mode.OFB=function(){var e=c.lib.BlockCipherMode.extend(),C=e.Encryptor=e.extend({processBlock:function(E,g){var u=this._cipher,l=u.blockSize,r=this._iv,t=this._keystream;r&&(t=this._keystream=r.slice(0),this._iv=void 0),u.encryptBlock(t,0);for(var h=0;h<l;h++)E[g+h]^=t[h]}});return e.Decryptor=C,e}(),c.mode.OFB})}(K0)),K0.exports}var X0={exports:{}},Hr;function pe(){return Hr||(Hr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.mode.ECB=function(){var e=c.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(C,E){this._cipher.encryptBlock(C,E)}}),e.Decryptor=e.extend({processBlock:function(C,E){this._cipher.decryptBlock(C,E)}}),e}(),c.mode.ECB})}(X0)),X0.exports}var G0={exports:{}},Rr;function De(){return Rr||(Rr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.pad.AnsiX923={pad:function(e,C){var E=e.sigBytes,g=C*4,u=g-E%g,l=E+u-1;e.clamp(),e.words[l>>>2]|=u<<24-l%4*8,e.sigBytes+=u},unpad:function(e){var C=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=C}},c.pad.Ansix923})}(G0)),G0.exports}var $0={exports:{}},Pr;function Fe(){return Pr||(Pr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.pad.Iso10126={pad:function(e,C){var E=C*4,g=E-e.sigBytes%E;e.concat(c.lib.WordArray.random(g-1)).concat(c.lib.WordArray.create([g<<24],1))},unpad:function(e){var C=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=C}},c.pad.Iso10126})}($0)),$0.exports}var j0={exports:{}},zr;function _e(){return zr||(zr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.pad.Iso97971={pad:function(e,C){e.concat(c.lib.WordArray.create([2147483648],1)),c.pad.ZeroPadding.pad(e,C)},unpad:function(e){c.pad.ZeroPadding.unpad(e),e.sigBytes--}},c.pad.Iso97971})}(j0)),j0.exports}var Z0={exports:{}},Wr;function be(){return Wr||(Wr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.pad.ZeroPadding={pad:function(e,C){var E=C*4;e.clamp(),e.sigBytes+=E-(e.sigBytes%E||E)},unpad:function(e){for(var C=e.words,E=e.sigBytes-1,E=e.sigBytes-1;E>=0;E--)if(C[E>>>2]>>>24-E%4*8&255){e.sigBytes=E+1;break}}},c.pad.ZeroPadding})}(Z0)),Z0.exports}var Q0={exports:{}},qr;function ge(){return qr||(qr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return c.pad.NoPadding={pad:function(){},unpad:function(){}},c.pad.NoPadding})}(Q0)),Q0.exports}var Y0={exports:{}},Tr;function ye(){return Tr||(Tr=1,function(H,R){(function(c,e,C){H.exports=e(L(),G())})(q,function(c){return function(e){var C=c,E=C.lib,g=E.CipherParams,u=C.enc,l=u.Hex,r=C.format;r.Hex={stringify:function(t){return t.ciphertext.toString(l)},parse:function(t){var h=l.parse(t);return g.create({ciphertext:h})}}}(),c.format.Hex})}(Y0)),Y0.exports}var V0={exports:{}},Ir;function ke(){return Ir||(Ir=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.BlockCipher,g=e.algo,u=[],l=[],r=[],t=[],h=[],x=[],f=[],o=[],v=[],s=[];(function(){for(var n=[],i=0;i<256;i++)i<128?n[i]=i<<1:n[i]=i<<1^283;for(var p=0,A=0,i=0;i<256;i++){var D=A^A<<1^A<<2^A<<3^A<<4;D=D>>>8^D&255^99,u[p]=D,l[D]=p;var _=n[p],P=n[_],d=n[P],F=n[D]*257^D*16843008;r[p]=F<<24|F>>>8,t[p]=F<<16|F>>>16,h[p]=F<<8|F>>>24,x[p]=F;var F=d*16843009^P*65537^_*257^p*16843008;f[D]=F<<24|F>>>8,o[D]=F<<16|F>>>16,v[D]=F<<8|F>>>24,s[D]=F,p?(p=_^n[n[n[d^_]]],A^=n[n[A]]):p=A=1}})();var B=[0,1,2,4,8,16,32,64,128,27,54],a=g.AES=E.extend({_doReset:function(){var n;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var i=this._keyPriorReset=this._key,p=i.words,A=i.sigBytes/4,D=this._nRounds=A+6,_=(D+1)*4,P=this._keySchedule=[],d=0;d<_;d++)d<A?P[d]=p[d]:(n=P[d-1],d%A?A>6&&d%A==4&&(n=u[n>>>24]<<24|u[n>>>16&255]<<16|u[n>>>8&255]<<8|u[n&255]):(n=n<<8|n>>>24,n=u[n>>>24]<<24|u[n>>>16&255]<<16|u[n>>>8&255]<<8|u[n&255],n^=B[d/A|0]<<24),P[d]=P[d-A]^n);for(var F=this._invKeySchedule=[],y=0;y<_;y++){var d=_-y;if(y%4)var n=P[d];else var n=P[d-4];y<4||d<=4?F[y]=n:F[y]=f[u[n>>>24]]^o[u[n>>>16&255]]^v[u[n>>>8&255]]^s[u[n&255]]}}},encryptBlock:function(n,i){this._doCryptBlock(n,i,this._keySchedule,r,t,h,x,u)},decryptBlock:function(n,i){var p=n[i+1];n[i+1]=n[i+3],n[i+3]=p,this._doCryptBlock(n,i,this._invKeySchedule,f,o,v,s,l);var p=n[i+1];n[i+1]=n[i+3],n[i+3]=p},_doCryptBlock:function(n,i,p,A,D,_,P,d){for(var F=this._nRounds,y=n[i]^p[0],k=n[i+1]^p[1],z=n[i+2]^p[2],W=n[i+3]^p[3],T=4,K=1;K<F;K++){var O=A[y>>>24]^D[k>>>16&255]^_[z>>>8&255]^P[W&255]^p[T++],N=A[k>>>24]^D[z>>>16&255]^_[W>>>8&255]^P[y&255]^p[T++],U=A[z>>>24]^D[W>>>16&255]^_[y>>>8&255]^P[k&255]^p[T++],b=A[W>>>24]^D[y>>>16&255]^_[k>>>8&255]^P[z&255]^p[T++];y=O,k=N,z=U,W=b}var O=(d[y>>>24]<<24|d[k>>>16&255]<<16|d[z>>>8&255]<<8|d[W&255])^p[T++],N=(d[k>>>24]<<24|d[z>>>16&255]<<16|d[W>>>8&255]<<8|d[y&255])^p[T++],U=(d[z>>>24]<<24|d[W>>>16&255]<<16|d[y>>>8&255]<<8|d[k&255])^p[T++],b=(d[W>>>24]<<24|d[y>>>16&255]<<16|d[k>>>8&255]<<8|d[z&255])^p[T++];n[i]=O,n[i+1]=N,n[i+2]=U,n[i+3]=b},keySize:256/32});e.AES=E._createHelper(a)}(),c.AES})}(V0)),V0.exports}var J0={exports:{}},Lr;function we(){return Lr||(Lr=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.WordArray,g=C.BlockCipher,u=e.algo,l=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],r=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],t=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],h=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],x=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=u.DES=g.extend({_doReset:function(){for(var B=this._key,a=B.words,n=[],i=0;i<56;i++){var p=l[i]-1;n[i]=a[p>>>5]>>>31-p%32&1}for(var A=this._subKeys=[],D=0;D<16;D++){for(var _=A[D]=[],P=t[D],i=0;i<24;i++)_[i/6|0]|=n[(r[i]-1+P)%28]<<31-i%6,_[4+(i/6|0)]|=n[28+(r[i+24]-1+P)%28]<<31-i%6;_[0]=_[0]<<1|_[0]>>>31;for(var i=1;i<7;i++)_[i]=_[i]>>>(i-1)*4+3;_[7]=_[7]<<5|_[7]>>>27}for(var d=this._invSubKeys=[],i=0;i<16;i++)d[i]=A[15-i]},encryptBlock:function(B,a){this._doCryptBlock(B,a,this._subKeys)},decryptBlock:function(B,a){this._doCryptBlock(B,a,this._invSubKeys)},_doCryptBlock:function(B,a,n){this._lBlock=B[a],this._rBlock=B[a+1],o.call(this,4,252645135),o.call(this,16,65535),v.call(this,2,858993459),v.call(this,8,16711935),o.call(this,1,1431655765);for(var i=0;i<16;i++){for(var p=n[i],A=this._lBlock,D=this._rBlock,_=0,P=0;P<8;P++)_|=h[P][((D^p[P])&x[P])>>>0];this._lBlock=D,this._rBlock=A^_}var d=this._lBlock;this._lBlock=this._rBlock,this._rBlock=d,o.call(this,1,1431655765),v.call(this,8,16711935),v.call(this,2,858993459),o.call(this,16,65535),o.call(this,4,252645135),B[a]=this._lBlock,B[a+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function o(B,a){var n=(this._lBlock>>>B^this._rBlock)&a;this._rBlock^=n,this._lBlock^=n<<B}function v(B,a){var n=(this._rBlock>>>B^this._lBlock)&a;this._lBlock^=n,this._rBlock^=n<<B}e.DES=g._createHelper(f);var s=u.TripleDES=g.extend({_doReset:function(){var B=this._key,a=B.words;if(a.length!==2&&a.length!==4&&a.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var n=a.slice(0,2),i=a.length<4?a.slice(0,2):a.slice(2,4),p=a.length<6?a.slice(0,2):a.slice(4,6);this._des1=f.createEncryptor(E.create(n)),this._des2=f.createEncryptor(E.create(i)),this._des3=f.createEncryptor(E.create(p))},encryptBlock:function(B,a){this._des1.encryptBlock(B,a),this._des2.decryptBlock(B,a),this._des3.encryptBlock(B,a)},decryptBlock:function(B,a){this._des3.decryptBlock(B,a),this._des2.encryptBlock(B,a),this._des1.decryptBlock(B,a)},keySize:192/32,ivSize:64/32,blockSize:64/32});e.TripleDES=g._createHelper(s)}(),c.TripleDES})}(J0)),J0.exports}var rr={exports:{}},Or;function Se(){return Or||(Or=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.StreamCipher,g=e.algo,u=g.RC4=E.extend({_doReset:function(){for(var t=this._key,h=t.words,x=t.sigBytes,f=this._S=[],o=0;o<256;o++)f[o]=o;for(var o=0,v=0;o<256;o++){var s=o%x,B=h[s>>>2]>>>24-s%4*8&255;v=(v+f[o]+B)%256;var a=f[o];f[o]=f[v],f[v]=a}this._i=this._j=0},_doProcessBlock:function(t,h){t[h]^=l.call(this)},keySize:256/32,ivSize:0});function l(){for(var t=this._S,h=this._i,x=this._j,f=0,o=0;o<4;o++){h=(h+1)%256,x=(x+t[h])%256;var v=t[h];t[h]=t[x],t[x]=v,f|=t[(t[h]+t[x])%256]<<24-o*8}return this._i=h,this._j=x,f}e.RC4=E._createHelper(u);var r=g.RC4Drop=u.extend({cfg:u.cfg.extend({drop:192}),_doReset:function(){u._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)l.call(this)}});e.RC4Drop=E._createHelper(r)}(),c.RC4})}(rr)),rr.exports}var er={exports:{}},Ur;function me(){return Ur||(Ur=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.StreamCipher,g=e.algo,u=[],l=[],r=[],t=g.Rabbit=E.extend({_doReset:function(){for(var x=this._key.words,f=this.cfg.iv,o=0;o<4;o++)x[o]=(x[o]<<8|x[o]>>>24)&16711935|(x[o]<<24|x[o]>>>8)&4278255360;var v=this._X=[x[0],x[3]<<16|x[2]>>>16,x[1],x[0]<<16|x[3]>>>16,x[2],x[1]<<16|x[0]>>>16,x[3],x[2]<<16|x[1]>>>16],s=this._C=[x[2]<<16|x[2]>>>16,x[0]&4294901760|x[1]&65535,x[3]<<16|x[3]>>>16,x[1]&4294901760|x[2]&65535,x[0]<<16|x[0]>>>16,x[2]&4294901760|x[3]&65535,x[1]<<16|x[1]>>>16,x[3]&4294901760|x[0]&65535];this._b=0;for(var o=0;o<4;o++)h.call(this);for(var o=0;o<8;o++)s[o]^=v[o+4&7];if(f){var B=f.words,a=B[0],n=B[1],i=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360,p=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,A=i>>>16|p&4294901760,D=p<<16|i&65535;s[0]^=i,s[1]^=A,s[2]^=p,s[3]^=D,s[4]^=i,s[5]^=A,s[6]^=p,s[7]^=D;for(var o=0;o<4;o++)h.call(this)}},_doProcessBlock:function(x,f){var o=this._X;h.call(this),u[0]=o[0]^o[5]>>>16^o[3]<<16,u[1]=o[2]^o[7]>>>16^o[5]<<16,u[2]=o[4]^o[1]>>>16^o[7]<<16,u[3]=o[6]^o[3]>>>16^o[1]<<16;for(var v=0;v<4;v++)u[v]=(u[v]<<8|u[v]>>>24)&16711935|(u[v]<<24|u[v]>>>8)&4278255360,x[f+v]^=u[v]},blockSize:128/32,ivSize:64/32});function h(){for(var x=this._X,f=this._C,o=0;o<8;o++)l[o]=f[o];f[0]=f[0]+1295307597+this._b|0,f[1]=f[1]+3545052371+(f[0]>>>0<l[0]>>>0?1:0)|0,f[2]=f[2]+886263092+(f[1]>>>0<l[1]>>>0?1:0)|0,f[3]=f[3]+1295307597+(f[2]>>>0<l[2]>>>0?1:0)|0,f[4]=f[4]+3545052371+(f[3]>>>0<l[3]>>>0?1:0)|0,f[5]=f[5]+886263092+(f[4]>>>0<l[4]>>>0?1:0)|0,f[6]=f[6]+1295307597+(f[5]>>>0<l[5]>>>0?1:0)|0,f[7]=f[7]+3545052371+(f[6]>>>0<l[6]>>>0?1:0)|0,this._b=f[7]>>>0<l[7]>>>0?1:0;for(var o=0;o<8;o++){var v=x[o]+f[o],s=v&65535,B=v>>>16,a=((s*s>>>17)+s*B>>>15)+B*B,n=((v&4294901760)*v|0)+((v&65535)*v|0);r[o]=a^n}x[0]=r[0]+(r[7]<<16|r[7]>>>16)+(r[6]<<16|r[6]>>>16)|0,x[1]=r[1]+(r[0]<<8|r[0]>>>24)+r[7]|0,x[2]=r[2]+(r[1]<<16|r[1]>>>16)+(r[0]<<16|r[0]>>>16)|0,x[3]=r[3]+(r[2]<<8|r[2]>>>24)+r[1]|0,x[4]=r[4]+(r[3]<<16|r[3]>>>16)+(r[2]<<16|r[2]>>>16)|0,x[5]=r[5]+(r[4]<<8|r[4]>>>24)+r[3]|0,x[6]=r[6]+(r[5]<<16|r[5]>>>16)+(r[4]<<16|r[4]>>>16)|0,x[7]=r[7]+(r[6]<<8|r[6]>>>24)+r[5]|0}e.Rabbit=E._createHelper(t)}(),c.Rabbit})}(er)),er.exports}var xr={exports:{}},Nr;function He(){return Nr||(Nr=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.StreamCipher,g=e.algo,u=[],l=[],r=[],t=g.RabbitLegacy=E.extend({_doReset:function(){var x=this._key.words,f=this.cfg.iv,o=this._X=[x[0],x[3]<<16|x[2]>>>16,x[1],x[0]<<16|x[3]>>>16,x[2],x[1]<<16|x[0]>>>16,x[3],x[2]<<16|x[1]>>>16],v=this._C=[x[2]<<16|x[2]>>>16,x[0]&4294901760|x[1]&65535,x[3]<<16|x[3]>>>16,x[1]&4294901760|x[2]&65535,x[0]<<16|x[0]>>>16,x[2]&4294901760|x[3]&65535,x[1]<<16|x[1]>>>16,x[3]&4294901760|x[0]&65535];this._b=0;for(var s=0;s<4;s++)h.call(this);for(var s=0;s<8;s++)v[s]^=o[s+4&7];if(f){var B=f.words,a=B[0],n=B[1],i=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360,p=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,A=i>>>16|p&4294901760,D=p<<16|i&65535;v[0]^=i,v[1]^=A,v[2]^=p,v[3]^=D,v[4]^=i,v[5]^=A,v[6]^=p,v[7]^=D;for(var s=0;s<4;s++)h.call(this)}},_doProcessBlock:function(x,f){var o=this._X;h.call(this),u[0]=o[0]^o[5]>>>16^o[3]<<16,u[1]=o[2]^o[7]>>>16^o[5]<<16,u[2]=o[4]^o[1]>>>16^o[7]<<16,u[3]=o[6]^o[3]>>>16^o[1]<<16;for(var v=0;v<4;v++)u[v]=(u[v]<<8|u[v]>>>24)&16711935|(u[v]<<24|u[v]>>>8)&4278255360,x[f+v]^=u[v]},blockSize:128/32,ivSize:64/32});function h(){for(var x=this._X,f=this._C,o=0;o<8;o++)l[o]=f[o];f[0]=f[0]+1295307597+this._b|0,f[1]=f[1]+3545052371+(f[0]>>>0<l[0]>>>0?1:0)|0,f[2]=f[2]+886263092+(f[1]>>>0<l[1]>>>0?1:0)|0,f[3]=f[3]+1295307597+(f[2]>>>0<l[2]>>>0?1:0)|0,f[4]=f[4]+3545052371+(f[3]>>>0<l[3]>>>0?1:0)|0,f[5]=f[5]+886263092+(f[4]>>>0<l[4]>>>0?1:0)|0,f[6]=f[6]+1295307597+(f[5]>>>0<l[5]>>>0?1:0)|0,f[7]=f[7]+3545052371+(f[6]>>>0<l[6]>>>0?1:0)|0,this._b=f[7]>>>0<l[7]>>>0?1:0;for(var o=0;o<8;o++){var v=x[o]+f[o],s=v&65535,B=v>>>16,a=((s*s>>>17)+s*B>>>15)+B*B,n=((v&4294901760)*v|0)+((v&65535)*v|0);r[o]=a^n}x[0]=r[0]+(r[7]<<16|r[7]>>>16)+(r[6]<<16|r[6]>>>16)|0,x[1]=r[1]+(r[0]<<8|r[0]>>>24)+r[7]|0,x[2]=r[2]+(r[1]<<16|r[1]>>>16)+(r[0]<<16|r[0]>>>16)|0,x[3]=r[3]+(r[2]<<8|r[2]>>>24)+r[1]|0,x[4]=r[4]+(r[3]<<16|r[3]>>>16)+(r[2]<<16|r[2]>>>16)|0,x[5]=r[5]+(r[4]<<8|r[4]>>>24)+r[3]|0,x[6]=r[6]+(r[5]<<16|r[5]>>>16)+(r[4]<<16|r[4]>>>16)|0,x[7]=r[7]+(r[6]<<8|r[6]>>>24)+r[5]|0}e.RabbitLegacy=E._createHelper(t)}(),c.RabbitLegacy})}(xr)),xr.exports}var tr={exports:{}},Mr;function Re(){return Mr||(Mr=1,function(H,R){(function(c,e,C){H.exports=e(L(),t0(),a0(),r0(),G())})(q,function(c){return function(){var e=c,C=e.lib,E=C.BlockCipher,g=e.algo;const u=16,l=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],r=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var t={pbox:[],sbox:[]};function h(s,B){let a=B>>24&255,n=B>>16&255,i=B>>8&255,p=B&255,A=s.sbox[0][a]+s.sbox[1][n];return A=A^s.sbox[2][i],A=A+s.sbox[3][p],A}function x(s,B,a){let n=B,i=a,p;for(let A=0;A<u;++A)n=n^s.pbox[A],i=h(s,n)^i,p=n,n=i,i=p;return p=n,n=i,i=p,i=i^s.pbox[u],n=n^s.pbox[u+1],{left:n,right:i}}function f(s,B,a){let n=B,i=a,p;for(let A=u+1;A>1;--A)n=n^s.pbox[A],i=h(s,n)^i,p=n,n=i,i=p;return p=n,n=i,i=p,i=i^s.pbox[1],n=n^s.pbox[0],{left:n,right:i}}function o(s,B,a){for(let D=0;D<4;D++){s.sbox[D]=[];for(let _=0;_<256;_++)s.sbox[D][_]=r[D][_]}let n=0;for(let D=0;D<u+2;D++)s.pbox[D]=l[D]^B[n],n++,n>=a&&(n=0);let i=0,p=0,A=0;for(let D=0;D<u+2;D+=2)A=x(s,i,p),i=A.left,p=A.right,s.pbox[D]=i,s.pbox[D+1]=p;for(let D=0;D<4;D++)for(let _=0;_<256;_+=2)A=x(s,i,p),i=A.left,p=A.right,s.sbox[D][_]=i,s.sbox[D][_+1]=p;return!0}var v=g.Blowfish=E.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var s=this._keyPriorReset=this._key,B=s.words,a=s.sigBytes/4;o(t,B,a)}},encryptBlock:function(s,B){var a=x(t,s[B],s[B+1]);s[B]=a.left,s[B+1]=a.right},decryptBlock:function(s,B){var a=f(t,s[B],s[B+1]);s[B]=a.left,s[B+1]=a.right},blockSize:64/32,keySize:128/32,ivSize:64/32});e.Blowfish=E._createHelper(v)}(),c.Blowfish})}(tr)),tr.exports}(function(H,R){(function(c,e,C){H.exports=e(L(),B0(),se(),fe(),t0(),ce(),a0(),lr(),m0(),ve(),Er(),ue(),de(),he(),T0(),le(),r0(),G(),Be(),Ce(),Ae(),Ee(),pe(),De(),Fe(),_e(),be(),ge(),ye(),ke(),we(),Se(),me(),He(),Re())})(q,function(c){return c})})(or);var Kr=or.exports;const C0=ae(Kr),Pe=s0({__proto__:null,default:C0},[Kr]),ze="(function(){const self=this;const transAudioData={to16kHz(audioData){var data=new Float32Array(audioData);var fitCount=Math.round(data.length*(16000/44100));var newData=new Float32Array(fitCount);var springFactor=(data.length-1)/(fitCount-1);newData[0]=data[0];for(let i=1;i<fitCount-1;i++){var tmp=i*springFactor;var before=Math.floor(tmp).toFixed();var after=Math.ceil(tmp).toFixed();var atPoint=tmp-before;newData[i]=data[before]+(data[after]-data[before])*atPoint}newData[fitCount-1]=data[data.length-1];return newData},to16BitPCM(input){var dataLength=input.length*(16/8);var dataBuffer=new ArrayBuffer(dataLength);var dataView=new DataView(dataBuffer);var offset=0;for(var i=0;i<input.length;i++,offset+=2){var s=Math.max(-1,Math.min(1,input[i]));dataView.setInt16(offset,s<0?s*0x8000:s*0x7fff,true)}return dataView},transcode(audioData){let output=transAudioData.to16kHz(audioData);output=transAudioData.to16BitPCM(output);output=Array.from(new Uint8Array(output.buffer));self.postMessage(output)}};this.onmessage=function(e){transAudioData.transcode(e.data)}})()";class Xr{constructor(R){this.APPID=R.APPID||"",this.APISecret=R.APISecret||"",this.APIKey=R.APIKey||"",this.url=R.url||"wss://iat-api.xfyun.cn/v2/iat",this.host=R.host||"iat-api.xfyun.cn",this.onTextChange=R.onTextChange||Function,this.onWillStatusChange=R.onWillStatusChange||Function,this.onError=R.onError,this.status="null",this.language=R.language||"zh_cn",this.accent=R.accent||"mandarin",this.timer=null,this.streamRef=[],this.audioData=[],this.resultText="",this.resultTextTemp="",this.init()}getWebSocketUrl(){return new Promise((R,c)=>{const{url:e,host:C,APISecret:E,APIKey:g}=this;let u=new Date().toGMTString(),l="hmac-sha256",r="host date request-line",t=`host: ${C}
date: ${u}
GET /v2/iat HTTP/1.1`,h=C0.HmacSHA256(t,E),x=C0.enc.Base64.stringify(h),f=`api_key="${g}", algorithm="${l}", headers="${r}", signature="${x}"`,o=btoa(f);R(`${e}?authorization=${o}&date=${u}&host=${C}`)})}init(){try{if(!this.APPID||!this.APISecret||!this.APIKey)this.recorderError("对不起：请正确配置科大迅飞语音听写（流式版）WebAPI服务接口认证参数【APPID、APISecret、APIKey】信息！");else{const R=new Blob([ze],{type:"application/javascript"});this.webWorker=new Worker(URL.createObjectURL(R)),this.webWorker.onmessage=c=>{this.audioData.push(...c.data)}}}catch(R){this.recorderError("对不起：请在服务器环境如：WAMP、XAMPP、Phpstudy、http-server、WebServer等服务环境中运行！"+R)}finally{}}setStatus(R){this.onWillStatusChange&&this.status!==R&&this.onWillStatusChange(this.status,R),this.status=R}setResultText({resultText:R,resultTextTemp:c}={}){this.onTextChange&&this.onTextChange(c||R||""),R!==void 0&&(this.resultText=R),c!==void 0&&(this.resultTextTemp=c)}setParams({language:R,accent:c}={}){R&&(this.language=R),c&&(this.accent=c)}toBase64(R){let c="",e=new Uint8Array(R);for(let C=0;C<e.byteLength;C++)c+=String.fromCharCode(e[C]);return globalThis.btoa(c)}recorderError(R=""){console.error(R),this.onError&&this.onError(R)}webSocketSend(){if(this.webSocket.readyState!==1)return!1;const R=this.audioData.splice(0,1280),c={common:{app_id:this.APPID},business:{language:this.language,domain:"iat",accent:this.accent,vad_eos:5e3,dwa:"wpgs"},data:{status:0,format:"audio/L16;rate=16000",encoding:"raw",audio:this.toBase64(R)}};this.webSocket.send(JSON.stringify(c)),this.timer=setInterval(()=>{if(this.webSocket.readyState!==1)return this.audioData=[],clearInterval(this.timer),!1;if(this.audioData.length===0)return this.status==="end"&&(this.webSocket.send(JSON.stringify({data:{status:2,format:"audio/L16;rate=16000",encoding:"raw",audio:""}})),this.audioData=[],clearInterval(this.timer)),!1;this.webSocket.send(JSON.stringify({data:{status:1,format:"audio/L16;rate=16000",encoding:"raw",audio:this.toBase64(this.audioData.splice(0,1280))}}))},40)}connectWebSocket(){return this.getWebSocketUrl().then(R=>{if("WebSocket"in window)this.webSocket=new WebSocket(R);else if("MozWebSocket"in window)this.webSocket=new globalThis.MozWebSocket(R);else return this.recorderError("对不起：当前Web浏览器不支持 WebSocket通信，请升级或使用其他Web浏览器后尝试！");this.setStatus("init"),this.webSocket.onopen=c=>{this.setStatus("ing"),setTimeout(()=>{this.webSocketSend()},500)},this.webSocket.onmessage=c=>{const e=JSON.parse(c.data);if(e.data&&e.data.result){let C=e.data.result,E="",g=C.ws;for(let u=0;u<g.length;u++)E=E+g[u].cw[0].w;C.pgs?(C.pgs==="apd"&&this.setResultText({resultText:this.resultTextTemp}),this.setResultText({resultTextTemp:this.resultText+E})):this.setResultText({resultText:this.resultText+E})}e.code===0&&e.data.status===2&&this.webSocket.close(),e.code!==0&&this.webSocket.close()},this.webSocket.onerror=c=>{this.stop(),this.recorderError(c)},this.webSocket.onclose=c=>{this.stop()}})}recorderInit(){try{this.audioContext=this.audioContext?this.audioContext:new(window.AudioContext||window.webkitAudioContext),this.audioContext.resume()}finally{if(!this.audioContext)return this.recorderError("对不起：当前Web浏览器不支持WebAudioAPI，请升级或使用其他Web浏览器后尝试！")}const R=e=>{const C=this.audioContext.createScriptProcessor(0,1,1);C.onaudioprocess=E=>{if(this.status==="ing")try{this.webWorker.postMessage(E.inputBuffer.getChannelData(0))}catch(g){console.log(g)}},this.audioContext.createMediaStreamSource(e).connect(C),C.connect(this.audioContext.destination),this.connectWebSocket()},c=e=>{this.recorderError("对不起： 录音权限获取失败，请检查用户是否允许麦克风使用受权！"),this.audioContext&&this.audioContext.close(),this.audioContext=void 0,this.webSocket&&this.webSocket.readyState===1&&this.webSocket.close()};if(navigator.getUserMedia=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia,navigator.getUserMedia)navigator.getUserMedia({video:!1,audio:!0},e=>{R(e)},e=>{c()});else if(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)navigator.mediaDevices.getUserMedia({video:!1,audio:!0}).then(e=>{R(e)}).catch(e=>{c()});else return navigator.userAgent.toLowerCase().match(/chrome/)&&location.origin.indexOf("https://")<0?this.recorderError("对不起：由于获取浏览器录音功能，因安全性问题，所以需要在 本地开发（localhost 或 127.0.0.1） 或 https服务环境下才能获取用户受权！"):this.recorderError("对不起：未识别到录音设备（麦克风） 或 用户阻止了麦克风使用受权！，请检查录音设备是否存在、用户是否允许受权！"),this.audioContext&&this.audioContext.close(),!1}start(){this.setResultText({resultText:"",resultTextTemp:""}),this.audioContext?(this.audioContext.resume(),this.connectWebSocket()):this.recorderInit()}stop(){try{this.setStatus("end"),/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgen)||this.audioContext&&this.audioContext.suspend()}catch(R){this.recorderError("停止录音失败！"+R)}}}const We={CryptoJS:C0,XfVoiceDictation:Xr};J.CryptoJS=Pe,J.XfVoiceDictation=Xr,J.default=We,Object.defineProperties(J,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
