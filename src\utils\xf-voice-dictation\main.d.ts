export * as CryptoJS from 'crypto-js';
interface IOptions {
    APPID: string;
    APISecret: string;
    APIKey: string;
    onWillStatusChange: Function;
    onTextChange: Function;
    url?: string;
    host?: string;
    algorithm?: string;
    headers?: string;
    accent?: string;
    language?: string;
    onError?: Function;
}
/**
 * 讯飞语音听写 WebAPI 流式版
 */
export declare class XfVoiceDictation {
    APPID: string;
    APISecret: string;
    APIKey: string;
    url: string;
    host: string;
    onTextChange: Function;
    onWillStatusChange: Function;
    onError?: Function;
    webWorker: Worker;
    webSocket: WebSocket;
    audioContext: AudioContext;
    streamRef: Array<any>;
    audioData: Array<any>;
    timer: null;
    status: any;
    language: any;
    accent: any;
    resultText: string;
    resultTextTemp: string;
    constructor(opts: IOptions);
    getWebSocketUrl(): Promise<string>;
    init(): void;
    setStatus(status: any): void;
    setResultText({ resultText, resultTextTemp, }?: {
        resultText?: string;
        resultTextTemp?: string;
    }): void;
    setParams({ language, accent }?: {
        language?: string;
        accent?: string;
    }): void;
    toBase64(buffer: any): string;
    recorderError(error?: string): void;
    webSocketSend(): false | undefined;
    connectWebSocket(): Promise<void>;
    recorderInit(): false | void;
    start(): void;
    stop(): void;
}
declare const _default: {
    CryptoJS: any;
    XfVoiceDictation: typeof XfVoiceDictation;
};
export default _default;
