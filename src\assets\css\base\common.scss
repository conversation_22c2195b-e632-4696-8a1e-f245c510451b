// 宋体
@font-face {
  font-family: SongtiGb2312;
  src: url("../../font/customer/GB2312.ttf") format("truetype");
}

@font-face {
  font-family: SourceHanSerifC;
  src: url("../../font/customer/SourceHanSerifCN-Regular.otf") format("truetype");
}
html,
body,
.com-app-wrap {
  height: 100%;
  color: var(--text-5);
}
svg {
  outline: none;
}
body {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 修改自动填充时的背景颜色 */
input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  transition: background-color 5000s ease-in-out 0s !important;
}
::-webkit-scrollbar {
  background-color: transparent;
  width: 6px;
}
// 横向滚动条轨道
::-webkit-scrollbar:horizontal {
  height: 6px;  // 控制横向滚动条高度
  background-color: rgba(var(--fill-rbg-12), 0.2);
}

// 横向滚动条滑块
::-webkit-scrollbar-thumb:horizontal {
  background-color: rgba(var(--fill-rgb-12), 0.2);
  border-radius: var(--border-radius-5);
  &:hover {
    background-color: rgba(var(--fill-rgb-12), 0.3); // 增加悬停效果
  }
}
:hover::-webkit-scrollbar-thumb {
  background-color: rgba(var(--fill-rgb-12), 0.2);
  border-radius: var(--border-radius-5);
}
.ant-drawer-content-wrapper .ant-drawer-body {
  padding: 0px !important;
}

.ant-drawer-content-wrapper {
  border-radius: var(--border-radius-16) !important;
}

.base-font {
  font-family: Source Han Sans;
  font-weight: normal;
  line-height: normal;
}
// 失效
.base-invalid-tag {
  font-size: var(--font-12);
  font-weight: normal;
  color: var(--error-6);
  border-radius: var(--border-radius-4);
  padding: 0 8px;
  background: var(--error-1);
  box-sizing: border-box;
  border: 1px solid var(--error-4);
  height: 20px;
  margin-left: 8px;
  flex-shrink: 0;
}
