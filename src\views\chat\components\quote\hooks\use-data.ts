import NationalPurseItem from '../national-purse-item.vue'
import RawItem from '../raw-item.vue'
import UserGuideItem from '../user-guide-item.vue'
import CaseItem from '../case-item.vue'
import NegativeItem from '../negative-item.vue'
// 组件类型对应
const comTypeMap: Record<string, any> = {
  lawsList: RawItem,// 法规类型
  flowchartsList: UserGuideItem, // 流程图类型
  operatingManualList: UserGuideItem,//操作手册类型
  treasuryDepartmentList: NationalPurseItem,// 国库司类型
  typicalCaseList: CaseItem,// 典型案例类型
  negativesList: NegativeItem// 负面清单类型
}

// 类型标识对应
const cleanTypeMap: Record<string, any> =  {
  clean_negatives: '天津大学政府采购负面清单',
  clean_flowcharts: '流程图指南',
  clean_operating_manual: '操作手册',
  clean_regulations: '法律法规',
  clean_treasury_department: '国库司问答',
  clean_guide_case: '典型案例'
}
export const getCleanTypeTitle = (val: string,defaultVal='引用依据')=> {
  let res = defaultVal
  for(const key in cleanTypeMap) {
    if(val.includes(key)) res = cleanTypeMap[key]
  }
  return res
}
// 是否流程图负面清单 无菜单
export const judgeNoMenu = (val: string)=> {
  let res = false
  if(val && (val.includes('clean_flowcharts') || val.includes('clean_negatives'))) res = true
  return res
}
export  const getComType = (key: string)=> {
  return comTypeMap[key] || CaseItem
}
