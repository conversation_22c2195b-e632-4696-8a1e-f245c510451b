export function extractMap(content: string,startTag:string, endTag: string ): string {
    if(!content) return ''
    const startIndex = content.indexOf(startTag);
    const endIndex = content.indexOf(endTag);

    if (startIndex === -1) {
      return ''; // 未找到标签时返回空字符串
    }
    // 处理结束标签不存在的情况
    const adjustedEndIndex = endIndex === -1 ? content.length : endIndex;
    // 加上起始标签长度确保截取到完整内容
    return content.substring(
      startIndex + startTag.length,
      adjustedEndIndex
    ).trim();
}