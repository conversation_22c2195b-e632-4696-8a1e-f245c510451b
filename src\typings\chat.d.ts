export interface ChatHistory {
  dateTime: string
  text: string
  like?: boolean
  abort?: boolean
  error?: boolean
}

export interface History {
  title: string
  isEdit: boolean
  uuid: string
}


/**
 * isStop 是否被终止
 * typing 是否打字中
 * likeType 1=点赞 2=点踩
 * suggestion 内容审核结果
 * isLegal 是否合法
 */
export interface ChatAnswer {
  answerId: string
  role?: string
  content: string
  isStop: boolean
  typing: boolean
  likeType: number
  state: string
  suggestion: string
  messageId?: string
  isLegal: boolean
}

export interface ChatAnswers {
  chatAnswerList: ChatAnswer[]
}

/**
 * suggestion 合法=pass 非法=block
 */
export interface Question {
  questionId: string
  type?: string
  content: string
  suggestion?: string
}

export interface Chat {
  question: Question,
  chatAnswers: {
    // currentIndex: number,
    chatAnswerList: ChatAnswer[]
  }
}

export interface Website {
  name: string
  url: string
}
