<template>
  <div v-if="!answer.sensitive" class="answer-toolbar">
    <div v-if="answer.isTerminate" class="t">回答已停止生成</div>
    <div />
    <div class="btns">
      <!-- 最后一条有重新生成且加载完毕 -->
      <a-tooltip v-if="index === state.qaList.length-1 && answer.isFinish" title="重新生成回答">
        <a-button type="link" @click="onClickRegeneration">
          <template #icon>
            <svg-icon icon="icon-regenerate" class="w-[24px] h-[24px]" />
          </template>
        </a-button>
      </a-tooltip>
      <template v-if="!answer.isTerminate">
        <a-tooltip v-if="answer.content" title="复制">
          <a-button type="link" @click="doCopy">
            <template #icon>
              <svg-icon icon="icon-copy" class="w-[24px] h-[24px]" />
            </template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="点赞">
          <a-button type="link" @click="doLike(1)">
            <template #icon>
              <svg-icon :icon="iconLike" class="w-[24px] h-[24px]" />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 点踩 -->
        <a-popover v-model:open="dislikeModalVisible" title="您的反馈将帮助我们优化进步" trigger="click" @open-change="onClickPopover">
          <template #content>
            <div @keydown="handleTimerClick" @click="handleTimerClick">
              <a-form id="dislike-popover-form" :model="form">
                <div class="dislike-popover-form-opts">
                  <a-checkable-tag
                    v-for="reason in state.dislikeReason"
                    :key="reason.id"
                    v-model:checked="reason.checked"
                    class="dislike-popover-form-opt"
                    :class="!reason.checked ? '':'dislike-popover-form-opt-checked'"
                    @change="(checked:any) => handleChange(reason.reason)">
                    {{ reason.reason }}
                  </a-checkable-tag>
                </div>

                <a-textarea
                  v-model:value="form.userDescription" placeholder="其他" :maxlength="100"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                  class="textarea-other"
                />
              </a-form>

              <div id="dislike-popover-btns">
                <a-button class="com" @click="onCancelDoLike">取消</a-button>
                <a-button type="primary" class="com" @click="onConfirmDoLike">确定</a-button>
              </div>
            </div>
          </template>

          <a-tooltip title="点踩">
            <a-button type="link" @click="doLike(2)">
              <template #icon>
                <svg-icon :icon="iconDislike" class="w-[24px] h-[24px]" />
              </template>
            </a-button>
          </a-tooltip>
        </a-popover>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onUnmounted, reactive, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import {  apiMessageMark } from '@/api/chat'
import { copyToClip } from '@/utils/copy'

type Props = {
  answer: Record<string,any>
  index?: number// 问答对索引，已有返回结果
}
const props = withDefaults(defineProps<Props>(), {
  index: 0
})
const iconLike = ref()
const iconDislike = ref()

const { state, sendQuestion, getDislikeReason  } = useChatGptInject()

// 重新生成
const onClickRegeneration = ()=> {
  const { question, questionId, chatId } = props.answer
  state.question = question
  state.chatId =  chatId
  state.questionId =  questionId
  state.isRegenerate = true
  sendQuestion(state.question)
}

//  入参
const form = reactive<any>({
  markType: props.answer.markType ?? 0, // 1点赞 2点踩
  actType: props.answer.markType ? 1 : 2, // 1确认 2取消
  dislikeReasonList: [],
  userDescription: ''
})
// 是否点赞
const isLike = ref<boolean>(props.answer.markType === 1)
/**
 * 点赞/点踩
 * @param markType 点赞类型
 */
const doLike = async (markType?:number)=> {
  // 点击的是同一个（点赞或点踩）操作在确认和取消中切换
  if(form.markType === markType) form.actType = (form.actType === 1) ? 2 : 1
  else form.actType = 1
  form.markType = (form.markType === markType) ? 0 : markType
  isLike.value = markType === 1 ? true : false
  messageMark()
}

const dislikeModalVisible = ref(false)
// 点踩取消
const onCancelDoLike = ()=> {
  dislikeModalVisible.value = false
}
// 点踩显示 10s后弹框无操作关闭
const dislikePopoverTimer = ref()
const onClickPopover = (visible: boolean) => {
  if (!(form.actType === 1 && form.markType === 2)) {
    dislikeModalVisible.value = false
  } else {
    dislikeModalVisible.value = visible
    handleTimerClick()
  }
  for(let item of state.dislikeReason) {
    item.checked = false
  }
  form.dislikeReasonList = []
  form.userDescription = ''
}

const handleTimerClick = ()=> {
  clearTimeout(dislikePopoverTimer.value)
  dislikePopoverTimer.value = setTimeout(function() {
    onCancelDoLike()
  }, 10*1000)
}
/**
 * 点踩标签改变
 */
function handleChange(reason: string) {
  const index = form.dislikeReasonList.findIndex(x=>x===reason)
  if(index===-1) form.dislikeReasonList.push(reason)
  else form.dislikeReasonList.splice(index,1)

}
// 点踩确认
const onConfirmDoLike = async ()=> {
  messageMark()
  onCancelDoLike()
}
const messageMark = async ()=> {
  const { questionId, answerId } = props.answer
  await apiMessageMark({ questionId, answerId, ...form, markType: isLike.value ? 1 : 2 })

  if (form.actType === 1 && form.markType === 1) {
    message.info('感谢您的认可')
  } else if (form.actType === 1 && form.markType === 2) {
    message.info('感谢您的反馈')
  }
}
/**
 * 复制
 */
function doCopy() {
  const content = props.answer.content ?? ''
  if (!content) return
  copyToClip(content)
    .then(() => {
      message.success('复制成功')
    })
    .catch(() => {
      message.error('复制失败')
    })
}
getDislikeReason()

watch(
  form,
  (val) => {
    if (val.markType == 1) iconLike.value = 'icon-hand-like-active'
    else iconLike.value = 'icon-like'
  },
  { immediate: true }
)

watch(
  form,
  (val) => {
    if (val.markType == 2) iconDislike.value = 'icon-hand-dislike-active'
    else iconDislike.value = 'icon-dislike'
  },
  { immediate: true }
)

onUnmounted(()=> {
  clearTimeout(dislikePopoverTimer.value)
  dislikePopoverTimer.value = null
})
</script>

<style scoped lang="scss">
.answer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .t {
    flex-grow: 1;
    font-family: 思源黑体;
    font-size: var(--font-12);
    font-weight: normal;
    line-height: 21px;
    color: var(--fill-6);
  }

  .btns {
    height: 24px;
    display: flex;
    align-items: center;
  }
}

#dislike-popover-form {
  width: 224px;

  .dislike-popover-form-opts {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .dislike-popover-form-opt {
      width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-14);
      background: var(--fill-1);
      height: 28px;
    }

    .dislike-popover-form-opt-checked {
      background: var(--main-1);
      color: var(--main-6);
    }
  }

  .textarea-other {
    width: 100%;
    margin-top: 8px;
  }
}

#dislike-popover-btns {
  margin-top: 16px;
  width: 100%;
  gap: 8px;
  display: flex;
  justify-content: flex-end;

  .com {
    height: 28px;
    display: flex;
    align-items: center;
  }
}
</style>
