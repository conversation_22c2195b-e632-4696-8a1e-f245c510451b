<template>
  <div ref="lottieRef" :style="{ width, height }"/>
</template>

<script>
import {defineComponent, ref, onMounted} from 'vue'
import lottie from 'lottie-web'
import defaultJsonData from './404.json'

export default defineComponent({
  props: {
    width: {
      type: String,
      default: '100px'
    },
    height: {
      type: String,
      default: '100px'
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    jsonData: {
      type: Object,
      default: () => defaultJsonData
    },
    loop: {
      type: Boolean,
      default: true
    },
    renderer: {
      type: String,
      default: 'svg'
    },
    src: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    let lottieRef = ref(null)

    onMounted(() => {
      if (lottieRef.value) {
        lottie.loadAnimation({
          container: lottieRef.value,
          renderer: props.renderer,
          loop: props.loop,
          autoplay: props.autoplay,
          path: props.src,
          animationData: props.jsonData
        })
      }
    })

    return {
      lottieRef
    }
  }
})
</script>

