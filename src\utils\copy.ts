export function copyToClip(text: string) {
  return new Promise((resolve, reject) => {
    try {
      text = text.replace('```markdown\n', '').replace('```', '')
      text = text.replace(/<think>[\s\S]*?<\/think>/g, '')
      text = text.replace(/<summary_ref_map>[\s\S]*?<\/summary_ref_map>/g, '')
      const matchArr: RegExpMatchArray | null = text.match(/\[\^.*?\^\]/g)
      matchArr?.map(item => {
        text = text.replace(item, '')
      })

      // 去掉图片集
      const imgMatchArr: RegExpMatchArray | null = text.match(/\[#&images\[.*?\]images&#\]/g)
      imgMatchArr?.map(item => {
        text = text.replace(item, '')
      })
      const imgMatchArr2: RegExpMatchArray | null = text.match(/!\[\]\(.*?\)/g)
      imgMatchArr2?.map(item => {
        text = text.replace(item, '')
      })
      const input: HTMLTextAreaElement = document.createElement('textarea')
      input.setAttribute('readonly', 'readonly')
      input.value = text.trim()
      document.body.appendChild(input)
      input.select()
      if (document.execCommand('copy')) document.execCommand('copy')
      document.body.removeChild(input)
      resolve(text)
    } catch (error) {
      reject(error)
    }
  })
}
