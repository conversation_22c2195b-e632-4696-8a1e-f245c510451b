<template>
  <div class="quote-card-item-case">
    <div class="com-font title"><span v-if="data.sortId" class="sort">{{  data.sortId }}</span><span>{{ data.title }}</span><span v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span></div>
    <a-typography-paragraph :content="data.content" :ellipsis="{ rows: 5 }" class="com-font t" />

    <div v-if="data.url" class="toolbar">
      <a-button type="link" class="com-font btn" @click="go">
        <template #icon>
          <svg-icon icon="icon-link2" class="w-[20px] h-[20px]" />
        </template>
        <span>链接</span>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@/components'

interface Props {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {}
})

function go() {
  window.open(props.data.url, '_blank')
}
</script>

<style scoped lang="scss">
.quote-card-item-case {
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-bottom: 1px solid var(--line-1);
  margin-bottom: 12px;
  .com-font {
    font-family: Source Han Sans;
    font-size: var(--font-12);
    font-weight: normal;
    line-height: normal;
  }
  .sort {
    padding:0 4px;
    box-sizing: border-box;
    color: var(--fill-0);
    background-color: var(--main-5);
    border-radius: var(--border-radius-4);
    margin-right: 4px;
  }
  .title {
    font-family: 思源黑体;
    font-weight: bold;
    color: var(--text-4);
  }

  .t {
    line-height: 22px;
    color: var(--text-4);
    white-space: break-spaces;
  }

  .toolbar {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    .btn {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 18px;
      color:var(--fill-6);
      padding: unset;
    }

    .btn:hover {
      color: var(--main-6);
    }
  }
}
</style>
