import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'
// 首页不异步加载
import Chat from '@/views/chat/index.vue'
import config from '@/config'
export const dynamicRoutesMap: Record<string,any> = {
  AI_Chat: []
}
// 咨询路由
const aiNormalRoutes : RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Layout,
    redirect: { name: 'ChatIndex',replace:true },
    children: [
      {
        path: '/chat',
        name: 'ChatIndex',
        component: Chat
      },
      {
        path: '/robot-test',
        name: 'ChatRobotTest',
        meta: {
          title: '测试用的内嵌测试页面'
        },
        component: () => import('@/views/chat-robot/test.vue')
      },
      // 测试
      // {
      //   path: '/flow',
      //   name: 'flow',
      //   meta: {
      //     title: 'flow'
      //   },
      //   component: () => import('@/views/demo/flow/index.vue')
      // }
    ]
  },
  {
    path: '/robot-index',
    name: 'ChatRobotIndex',
    meta: {
      title: '咨询内嵌页'
    },
    component: () => import('@/views/chat-robot/index.vue')
  },
  {
    path: '/robot-reader',
    name: 'ChatRobotFileReader',
    meta: {
      title: '操作手册阅读器'
    },
    component: () => import('@/views/file-reader/index.vue')
  }
]
// 深圳路由
const aiShenzhenRoutes : RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Layout,
    redirect: { name: 'ChatIndex',replace:true },
    children: [
      {
        path: '/chat',
        name: 'ChatIndex',
        component: Chat
      }
    ]
  }
]
const demoRoutes: RouteRecordRaw[] = [
  // 专门放测试的
  {
    path: '/demo',
    name: 'demo',
    component: Layout,
    children: [
      
       
      {
        path: 'test',
        name: 'test',
        meta: {
          title: ''
        },
        component: () => import('@/views/demo/test/index.vue')
      },
      {
        path: 'robot',
        name: '内嵌页测试',
        meta: {
          title: ''
        },
        component: () => import('@/views/demo/robot/index.vue')
      }
    ]
  }
]
// 通用路由
const commonRoutes: RouteRecordRaw[] = [
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/agreement',
    name: 'Agreement',
    component: () => import('@/views/agreement/index.vue')
  },

  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404/index.vue')
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/views/exception/500/index.vue')
  }
]
// 开发环境路由
const devRoutes = config.env.VITE_ENV === 'dev' ? [...aiNormalRoutes,...demoRoutes]: config.env.VITE_ENV === 'shenzhen' ? [...aiShenzhenRoutes]: [...aiNormalRoutes]
export const routes: RouteRecordRaw[] = [
  ...devRoutes,
  ...commonRoutes
]
export const fallbackRoutes = [
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/404'
  }
]
