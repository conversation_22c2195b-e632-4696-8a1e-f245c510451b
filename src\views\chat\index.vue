<template>
  <a-flex  class="w-full h-full chat-right-container">
    <a-flex vertical class="w-full chat-right">
      <index-header />
      <index-center />
      <index-footer />
    </a-flex>
  </a-flex>
</template>

<script setup lang='ts'>
import IndexFooter from '@/views/chat/components/footer.vue'
import IndexHeader from '@/views/chat/components/header.vue'
import IndexCenter from '@/views/chat/components/center/index.vue'
import { useChatGptProvide } from '@/views/chat/hooks/use-chat'

defineOptions({
  name: 'ChatIndex'
})

useChatGptProvide()
</script>

<style lang="scss" scoped>
page {
  overflow: hidden;
}
.chat-right {
  padding: 20px;
  flex-grow: 1;
  align-items: center;
  border-radius: var(--border-radius-16) 0 0 var(--border-radius-16);
  background: var(--fill-0);
}
</style>
