<template>
  <div class="history">
    <a-drawer v-model:open="open" title="历史对话" placement="bottom" :closable="false" :get-container="false">
      <template #extra>
        <SvgIcon icon="icon-close" class="w-[20px] h-[20px] cursor-pointer" @click="cancel" />
      </template>
      <SearchBar ref="searchBarRef" v-model:input-value="searchValue" />
      <history-body :chat-group-data="chatGroupData" />
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useChatHistoryStore } from '@/store'
import HistoryBody from '@/views/chat-robot/components/history/body.vue'
import { getChatHistory } from '@/api/history'
import SearchBar from '@/views/chat/components/history/components/search-bar.vue'
import { SvgIcon } from '@/components'
import emitter from '@/utils/mitt'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'

interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})
const emits = defineEmits(['update:model-value'])

const open = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:model-value', val)
})

const chatHistoryStore = useChatHistoryStore()
const searchValue = ref('')
let handleSearchTimer: any = null
const chatGroupData = ref({})
const { getHistoryById, resetChat } = useChatGptInject()

function cancel() {
  emits('update:model-value', false)
}

function loadData() {
  let chatHistory = chatHistoryStore.chatHistory
  const searchText = searchValue.value?.trim()
  // bodyRef.value.showLoadingRef()
  if (!chatHistory) {
    getChatHistory({
      title: searchText
    }).then((res) => {
      const { data } = res
      chatHistoryStore.initChatHistory(data)
      chatGroupData.value = data
    }).catch((e) => {
      message.error('历史对话加载失败')
    }).finally(() => {
      // bodyRef.value.hideLoadingRef()
    })
  } else {
    let data: any = {}
    if (searchText) {
      for (let key in chatHistory) {
        data[key] = chatHistory[key].filter((item: any) => item.title.includes(searchText))
          .map((item: any) => {
            let newItem = Object.assign({}, item)
            newItem.htmlTitle = newItem.title.replaceAll(searchText, `<span class='highlight'>${searchText}</span>`)
            return newItem
          })
      }
      chatGroupData.value = data
    } else {
      chatGroupData.value = chatHistory
    }
  }
}

function handleSearch() {
  if (handleSearchTimer) {
    clearTimeout(handleSearchTimer)
  }
  handleSearchTimer = setTimeout(() => {
    loadData()
  }, 500)
}

function chatItemClick(id: string) {
  resetChat()
  getHistoryById(id)
  emits('update:model-value', false)
}

watch([() => open.value, () => searchValue.value], (val) => {
  if (val) {
    handleSearch()
  }
})

onMounted(() => {
  handleSearch()
  emitter.on('chatItemClick', chatItemClick)
})

onUnmounted(() => {
  emitter.off('chatItemClick', chatItemClick)
})
</script>
<style scoped lang="scss">
.history {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  border-radius: var(--border-radius-12);
  overflow: hidden;
  box-sizing: border-box;
  border: 1px solid var(--line-4);


  :deep(.ant-drawer) {
    .ant-drawer-content-wrapper {
      height: 526px !important;

      .ant-drawer-content {
        border-radius: var(--border-radius-12);
        box-sizing: border-box;
        border: 1px solid var(--line-4);

        .ant-drawer-header {
          border-bottom: unset;
        }

        .ant-drawer-body {
          padding-left: 16px !important;
          padding-right: 16px !important;
          padding-bottom: 16px !important;
          display: flex;
          flex-direction: column;
          gap: 24px;
        }
      }
    }

    .ant-modal {
      width: 280px !important;
    }
  }

  .search {
    border-radius: var(--border-radius-8);
    background: var(--fill-1);
    box-sizing: border-box;
    border: 1px solid var(--line-2);

    :deep(.ant-input) {
      background: unset;
    }
  }


  :deep(.ant-dropdown) {
    width: 128px;
  }
}
</style>
