<template>
  <div class="quote-card-item-user-guide">
    <div class="file">
      <span v-if="data.sortId" class="sort">{{  data.sortId }}</span>
      <svg-icon :icon="iconType" class="w-[24px] h-[24px] icon" />
      <span class="com-font filename">{{ data.fileName }}</span>
      <span v-if="data.fileStatus == 1" class="base-invalid-tag tag">失效</span>
    </div>

    <div class="toolbar">
      <a-button v-if="!isVisitor" type="link" class="com-font btn" @click="download">
        <template #icon>
          <svg-icon icon="icon-download1" class="w-[20px] h-[20px]" />
        </template>
        <span>下载</span>
      </a-button>
      <a-button type="link" class="com-font btn" @click="readFile(data,true)">
        <template #icon>
          <svg-icon icon="icon-view" class="w-[20px] h-[20px]" />
        </template>
        <span>查看原文</span>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed} from 'vue'
import { SvgIcon } from '@/components'
import { apiDownRefs } from '@/api/download'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import {  getCookie } from '@/utils/app-gateway'
interface Props {
  data: any
}

const props = withDefaults(defineProps<Props>(), {
  data: {
    suffixType: 0
  }
})

const iconType = computed(() => {
  if ('1'.includes(props.data.suffixType)) return 'icon-doc'
  else if ('2'.includes(props.data.suffixType)) return 'icon-pdf'
  else return 'icon-file'
})

const { readFile } = useChatGptInject()
const bosssoftCookie = getCookie()
const isVisitor = computed(() => bosssoftCookie?.isVisitor)
function download() {
  let fileId = props.data.fileId
  apiDownRefs(fileId)
}
</script>

<style scoped lang="scss">
.quote-card-item-user-guide {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .file {
    display: flex;
    gap: 8px;
    align-items: center;
    .icon {
      flex-shrink: 0;
    }

    .filename {
      font-size: var(--font-12);
      line-height: 18px;
      color: var(--text-4);
    }
  }
  .sort {
    padding:0 4px;
    box-sizing: border-box;
    color: var(--fill-0);
    background-color: var(--main-5);
    border-radius: var(--border-radius-4);
    margin-right: 4px;
  }
  .com-font {
    font-family: Source Han Sans;
    font-size: var(--font-14);
    font-weight: normal;
    line-height: normal;
  }

  .toolbar {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    .btn {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 18px;
      color:var(--fill-6);
      padding: unset;
      font-size: var(--font-12);
    }

    .btn:hover {
      color: var(--main-6);
    }
  }
}
</style>
