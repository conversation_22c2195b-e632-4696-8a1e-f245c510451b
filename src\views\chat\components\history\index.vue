<template>
  <BaseDrawer
    v-model="drawerVisible" class="history-index" :closable="false" width="calc(100% - 80px)"
    :mask-style="{background: 'transparent'}">
    <div class="history-container">
      <div class="history-container-main">
        <Header :history-count="chatCount" @close="close" />
        <SearchBar ref="searchBarRef" v-model:input-value="inputValue" />
        <Body ref="bodyRef" :chat-group-data="chatGroupData" />
      </div>
    </div>
  </BaseDrawer>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import BaseDrawer from '@/components/BaseDrawer/index.vue'
import Header from './components/header.vue'
import SearchBar from './components/search-bar.vue'
import Body from './components/body.vue'
import emitter from '@/utils/mitt'
import { getChatHistory } from '@/api/history'
import { message } from 'ant-design-vue'
import { useChatHistoryStore } from '@/store'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
const chatHistoryStore = useChatHistoryStore()


interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const drawerVisible = computed(function() {
  return props.visible
})
const emits = defineEmits(['update:visible', 'itemClick'])

const searchBarRef = ref()
const chatGroupData = ref({})
const inputValue = ref('')
const drawerWidth = ref('443px')
const bodyRef = ref()

const close = () => {
  searchBarRef.value?.clear()
  emits('update:visible', false)
}

function loadData() {
  let searchValue = inputValue.value?.trim()
  let chatHistory = chatHistoryStore.chatHistory
  bodyRef.value.showLoadingRef()
  if (!chatHistory) {
    getChatHistory({
      title: searchValue
    }).then((res) => {
      const { data } = res
      chatHistoryStore.initChatHistory(data)
      chatGroupData.value = data
    }).catch((e) => {
      message.error('历史会话加载失败')
    }).finally(() => {
      bodyRef.value.hideLoadingRef()
    })
  } else {
    let data = {}
    if (searchValue) {
      for (let key in chatHistory) {
        data[key] = chatHistory[key].filter(item => {
          return item.title.includes(searchValue)
        }).map(item => {
          let newItem = Object.assign({}, item)
          newItem.htmlTitle = newItem.title.replaceAll(searchValue, `<span class='highlight'>${searchValue}</span>`)
          return newItem
        })
      }
      chatGroupData.value = data
    } else {
      chatGroupData.value = chatHistory
    }
    bodyRef.value.hideLoadingRef()
  }

}

let handleSearchTimer = null

function handleSearch() {
  if (handleSearchTimer) {
    clearTimeout(handleSearchTimer)
  }
  handleSearchTimer = setTimeout(() => {
    loadData()
  }, 500)
}

const chatCount = computed(() => {
  return Object.values(chatGroupData.value).reduce((acc, cur) => {
    return acc + cur.length
  }, 0)
})

watch([() => props.visible, () => inputValue.value], (val) => {
  if (val) {
    drawerWidth.value = `${document.querySelector('.chat-right-container')?.clientWidth || 600}px`
    handleSearch()
  }
})

//
const { getHistoryById, resetChat,stopAnswer , state} = useChatGptInject()
const chatItemClick = async (id: string) => {
  if(state.isLoading) await stopAnswer()
  resetChat()
  getHistoryById(id)
  emits('itemClick', id)
}

const reLoadData = () => {
  handleSearch()
}
onMounted(() => {
  emitter.on('chatItemClick', chatItemClick)
  emitter.on('reLoadData', reLoadData)
  emitter.on('chatItemClick', close)
})

onUnmounted(() => {
  chatHistoryStore.clearChatHistory()
  emitter.off('chatItemClick', chatItemClick)
  emitter.off('reLoadData', reLoadData)
  emitter.off('chatItemClick', close)
})


</script>

<style lang="scss" scoped>
.history-index {

  .history-container {
    display: flex;
    justify-content: center;

    .history-container-main {
      max-width: 872px;
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      gap: 24px;
      padding: 16px 20px;
    }
  }
}
</style>

