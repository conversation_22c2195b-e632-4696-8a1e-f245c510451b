<template>
  <div id="chatRobotContain"></div>
</template>

<script setup>
import { onMounted  } from 'vue'
import config from '@/config'
import { getCookie } from '@/utils/app-gateway'
import { remoteScript } from '@/utils/tools'

onMounted(async () => {
  const bosssoftCookie = getCookie()
  // await remoteScript(config.env.VITE_CHAT_ROBOT_JS_URL)
  await remoteScript('/robotjsDist/index.js')
  // 文件加载后的操作
  if(bosssoftCookie && bosssoftCookie.token){
    const chatRobot = new ChatRobot({
      url: import.meta.env.VITE_CHAT_ROBOT_URL,
      token: bosssoftCookie.token,
      baseFontSize: 16,
      parentId: 'chatRobotContain',
      businessCode: 'AI_Chat',
      appId: bosssoftCookie.appId,
      userId: bosssoftCookie.userId,
      username: bosssoftCookie.userName,
      top: 97
    })
    chatRobot.show()
  }

})
</script>

<style scoped>
#chatRobotContain {
  height: 100%;
  overflow: hidden;
}
</style>
