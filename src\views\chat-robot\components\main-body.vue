<template>
  <div class="chat-robot-body">
    <div class="chat-container">
      <!-- 默认 -->
       <template v-if="state.isNewChat">
        <div class="w-full title">
          <img :src="loadThemeImg('logo.png')"  alt="logo"  width="40" height="40"/>
          <div class="t1 system-title">{{ systemInfo.title }}</div>
          <div class="t1 t2">基于阳光公采大模型的 AI 智能助手</div> 
        </div>
        <div class="tip">我可以帮您：</div>
        <ul class="list">
          <li v-for="(item,index) in list" :key="index" @click="doAsk(item.question)">
            <div class="text">{{item.title  }}</div>
            <a-typography-paragraph :content="item.question" :ellipsis="{ rows: 1 }" class="question" />
          </li>
        </ul>
      </template>
      <!-- 非新建对话 -->
      <template v-if="!state.isNewChat">
        <div id="QaRef" ref="qaRef" class="qa qa-container">
          <qa-item v-for="(item,index) in state.qaList" :key="index" :item="item" :index="index" is-chat-robot />
        </div>
      </template>
    </div>

    <div v-if="showInput" class="toolbar">
      <a-button type="link" size="small" @click="onclickHistory" class="btn-history">
        <template #icon>
          <svg-icon :icon="iconHistory" class="w-[16px] h-[16px]" />
        </template>
      </a-button>
      <a-button shape="round" size="small" @click="onclickNewChat" :disabled="illegal" class="btn-newchat">
        <template #icon>
          <img :src="loadThemeImg('newchat.svg')"  alt="新建对话"  class="w-[16px] h-[16px]"/>
        </template>
        <span>新建对话</span>
      </a-button>
    </div>
    <!-- 输入框 有且只有一个对话并且是敏感时，无输入框-->
    <chat-input v-if="showInput" is-chat-robot/>
    <history v-if="historyVisible" v-model="historyVisible" />
  </div>
</template>

<script setup lang='ts'>
import { ref, computed,watch  } from 'vue'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { useMenusStore,useChatHistoryStore, useRobotStore } from '@/store'
import { loadThemeImg } from '@/hooks/use-theme'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { getCookie } from '@/utils/app-gateway'
import ChatInput from '@/views/chat/components/center/chat-input.vue'
import QaItem from '@/views/chat/components/center/qa-item.vue'
import History from '@/views/chat-robot/components/history/index.vue'

defineOptions({
  name: 'ChatRobotMainBody'
}) 
// 系统信息
const menusStore = useMenusStore()
const systemInfo = computed(() => menusStore.systemInfo)
const chatHistoryStore = useChatHistoryStore()
const { resetChat, state, currentQaMap,resetParam, sendQuestion } = useChatGptInject()
// <!-- 输入框 有且只有一个对话并且是敏感时，无输入框-->
const showInput = computed(()=> !(state.qaList.length === 1 && currentQaMap.value.sensitive))
const router = useRouter()
const historyVisible = ref(false)
const illegal = computed(() => 1 < currentQaMap.value.hasSensitiveStyle)
const iconHistory = computed(() => (currentQaMap.value.hasSensitiveStyle) ? 'icon-history-disabled' :'icon-history')

function onclickNewChat() {
  if(state.isLoading) {
    message.warn('正在回答中，请稍后')
    return
  }
  if (state.isNewChat) {
    message.warn('已是最新对话')
  } else {
    resetChat()
    state.isNewChat = true
    chatHistoryStore.clearChatHistory()
    router.replace({ name: 'ChatRobotIndex' })
  }
}

function onclickHistory() {
  historyVisible.value = true
}
const doAsk = (val: string) => {
  resetParam()
  state.question = val
  state.inputVal = ''
  state.isNewChat = false
  sendQuestion(val)
}
const robotStore = useRobotStore()
if(getCookie()?.token) robotStore.getDefaultQuestionList()
const list = computed(()=> robotStore.defaultQuestionList)
</script>

<style lang="scss" scoped>
.chat-robot-body {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 8px;

  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center; 
  }

  .title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .t1 {
      font-family: 思源黑体;
      font-weight: bold;
      line-height: normal;
      color: var(--main-6);
    }

    .t2 {
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: normal;
      color: rgba(var(--fill-rgb-12),0.88);
    }
  }
  .tip {
    font-size: var(--font-14);
    color: rgba(var(--fill-rgb-12),0.45);
    margin-top: 24px;
  }
  .list {
    border-radius: var(--border-radius-2) var(--border-radius-16) var(--border-radius-16) var(--border-radius-16);
    background: linear-gradient(140deg, rgba(90, 151, 255, 0.2) -17%, rgba(174, 136, 255, 0.2) 119%);
    box-sizing: border-box;
    padding: 16px;
    margin-top: 10px;
    li {
      font-size: var(--font-14);
      padding: 8px 16px;
      border-radius: var(--border-radius-8);
      border: 1px solid var(--fill-0);
      background: rgba(var(--fill-rbg-0), 0.5);
      margin-top: 12px;
      cursor: pointer;
      &:first-child {
        margin-top: 0;
      }
      .text { 
        font-weight: bold;
        color: var(--text-5);
      }
      .question {
        color: var(--text-3);
      }
      :deep(.ant-typography){
        margin-bottom: 0;
      }
    }
  }

  .qa {
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 32px;
    flex-grow: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    box-sizing: border-box;
    padding-bottom: 16px;

    :deep(.qa-item > .question) {
      padding: 0 0 0 12px;

      .question-content {
        border-radius: var(--border-radius-12) var(--border-radius-2) var(--border-radius-12) var(--border-radius-12);
        font-family: Source Han Sans;
        font-size: var(--font-14);
        line-height: normal;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .btn-history {

    }

    .btn-newchat {
      display: flex;
      align-items: center;
      gap: 4px;
      background: var(--fill-1);
      font-family: Source Han Sans;
      font-size: var(--font-12);
      font-weight: normal;
      line-height: 24px;
      color: var(--text-5);
    }
  }

  .chat-input-box {
    margin: unset;
  }
}
</style>
