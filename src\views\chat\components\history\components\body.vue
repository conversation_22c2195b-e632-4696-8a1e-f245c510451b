<template>
  <div class="chat-container">
    <div class="loading-box" ref="loadingRef">
      <div class="w-[100px]">
        <Lottie loop width="100%" height="100%" :json-data="LoadingJson"/>
        <div class="loading-text">
          努力加载中…
        </div>
      </div>
    </div>

    <template v-for="(item,key) in chatGroupData" :key="key">
      <div class="chat-group" v-if="item && item.length > 0">
        <div class="time" v-if="key == 'topping'">
          <SvgIcon
            icon="icon-history-top"
            class="!h-[20px] !w-[20px]"
          />
        </div>
        <div class="time" v-else>{{ formatGroupName(key) }}</div>
        <div class="chat-item-container">
          <ChatItem :chatId="subItem.id" :title="subItem.title" v-for="subItem in item"
                    :key="subItem.id" :timeType="key" :html-title="subItem.htmlTitle" :last-active-time="subItem.lastActiveTime"/>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import ChatItem from "./chat-item.vue";
import {ref, computed, onMounted, onUnmounted} from 'vue'
import {Lottie, SvgIcon} from "@/components/";
import LoadingJson from "@/assets/json/loading.json";

interface Props {
  chatGroupData: Record<string,any>
}

const props = withDefaults(defineProps<Props>(), {
  chatGroupData: ()=> {
    return {}
  }
})
const loadingRef = ref(null)

function hideLoadingRef(){
  loadingRef.value.style.top = '-9999px'
}

function showLoadingRef(){
  loadingRef.value.style.top = '50%'
}


function formatGroupName(groupType: string) {
  switch (groupType) {
    case 'topping':
      return '置顶';
    case 'today':
      return '今日';
    case 'pastWeek':
      return '近七日';
    case 'pastYear':
      return '近一年';
    default:
      return '更久';
  }
}


defineExpose({
  hideLoadingRef,
  showLoadingRef
})

</script>

<style lang="scss" scoped>
.chat-container {
  flex-grow: 1;
  overflow-y: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  .loading-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform: translate(-50%, -60%);
    position: absolute;
    top: 50%;
    left: 50%;

    .loading-text {
      margin-top: 8px;
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      text-align: center;
      color: var(--fill-6);
    }
  }

  .chat-group {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 16px;

    .time {
      font-family: Source Han Sans;
      font-size: var(--font-12);
      font-weight: normal;
      line-height: normal;
      text-align: right;
      letter-spacing: 0em;
      color: var(--text-2);
    }

    .chat-item-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 8px;
    }
  }

  .chat-group:not(:first-child) {
    margin-top: 32px;
  }
}

.chat-container::-webkit-scrollbar {
  display: none;
}
</style>
