<template>
  <div
      v-show="visible" class="associate-input-container"
      :style="associateTop ? 'transform: translateY(-100%); top: -4px;' : 'transform: translateY(100%); bottom: -4px;'">
    <div class="close">
      <SvgIcon class="w-[24px] h-[24px]" icon="icon-close" @click="close"/>
    </div>
      <div class="associate-title">猜你想问</div>
      <div
        v-for="(item, index) in state.associateInputList" :key="index" class="associate-input-box"
        @click="onClickSendSimilar(item)">
        <div style="">
          <div class="status-point"></div>
        </div>
        <div class="content">{{ item.question }}</div>
        <div class="icon !h-[16px] !w-[16px]">
          <SvgIcon class="!h-[16px] !w-[16px]" icon="icon-arrow-right2" />
        </div>
      </div>
    </div>

</template>

<script setup lang='ts'>
import { ref, computed, watch, onBeforeMount } from 'vue'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { getSimilarQuestions } from '@/api/chat'
import { SvgIcon  } from '@/components/'

defineOptions({
  name: 'ChatIndexCenterAssociatePopover'
})

interface Props {
  modelValue: boolean
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})
const emits = defineEmits(['update:modelValue'])
const { state, currentQaMap,resetParam, sendQuestion } = useChatGptInject()
// 是否显示联想
const visible = computed({
  get(){
    return props.modelValue && !currentQaMap.value.loading && !state.disableAllInput && state.associateInputList.length
  },
  set(val) {
    emits('update:modelValue',val)
  }
})
const associateTop = computed(()=> !state.isNewChat)
const onClickSendSimilar = (item: any)=> {
  resetParam()
  state.question = item.question
  state.similarQuestionId = item.questionId
  state.isNewChat = false
  state.inputVal = ''
  sendQuestion(item.question)
  emits('update:modelValue',false)
}
// 联想列表
const similarLoading = ref(false)
const getSimilarList = async (val: string)=> {
  similarLoading.value = true
  state.associateInputList = []
  const { err, data } = await getSimilarQuestions({
    word: val.trim(),
    pageNum: 1,
    pageSize: 3
  })
  if(err) return
  state.associateInputList = data.questionList ?? []
}

function close(){
  visible.value = false
}

defineExpose({
  getSimilarList
})
</script>

<style lang="scss" scoped>
.associate-input-container {
    position: absolute;
    width: 100%;
    //height: 100%;
    z-index: 999;
    height: fit-content;
    border-radius: var(--border-radius-8);
    opacity: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 12px 8px;
    gap: 8px;
    background: var(--fill-0);
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.15);

  .close {
    position: absolute;
    right: 8px;
    top: 8px;
    cursor: pointer;
  }

    .associate-title {
      padding: 0px 12px;
      font-family: Source Han Sans;
      font-size: var(--font-14);
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      font-feature-settings: "kern" on;
      color: var(--fill-6);
    }

    .associate-input-box {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 5px 12px;

      .status-point {
        width: 4px;
        height: 4px;
        background: var(--main-6);
        border-radius: 50%;
        display: inline-block;
      }

      .content {
        font-family: Source Han Sans;
        font-size:  var(--font-14);
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;
        font-feature-settings: "kern" on;
        color: var(--text-5);
        flex: 1;
        width: 93%;
        word-wrap: break-word;
      }

      .icon {
        visibility: hidden;
      }
    }

    .associate-input-box:hover {
      background: var(--fill-1);
      border-radius: var(--border-radius-4);
      cursor: pointer;

      .icon {
        visibility: visible;
      }
    }
  }
</style>
