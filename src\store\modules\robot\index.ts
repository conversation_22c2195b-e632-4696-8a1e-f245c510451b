
import { defineStore } from 'pinia'
import { getQuestionList  } from '@/api/chat'
export const useRobotStore = defineStore('robot-store', {
  state: () => {
    return {
      defaultQuestionList: [] as any[] // 默认问题列表
    };
  },
  actions: {
    async getDefaultQuestionList(){
      const { err,data } = await getQuestionList() as any
      if(err) return
      this.defaultQuestionList = data ?? []
    }
  }
})
