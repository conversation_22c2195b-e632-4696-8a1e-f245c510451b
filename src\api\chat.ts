/**
 * 智能问答相关接口 2024.9.24
 */
import { http } from '@/services/http'
import { getCookie } from '@/utils/app-gateway'
import {  useMenusStore } from '@/store'
// import config from '@/config'
// sse流式接口
export const sendQuestionByEventsUrl = '/api/baseChatMessage/v1/sendQuestionByEvents'
export const sendQuestionByEventsTiandaUrl = '/tianda/baseChatMessage/v1/sendQuestionByEvents'
/**
 * sse停止回答
 */
export const sseApiStopAnswerByEvents = (data: { answerId: string; content: string;thinkingTime:string}) =>
{
  const menusStore = useMenusStore()
  const prefix = menusStore.isTianda ? '/tianda' : '/api'
  return http({
    url: `${prefix}/baseChatMessage/v1/stopAnswerNew`,
    data,
    method: 'post'
  })
}
  /**
 * 获取相似问题
 * @param params
 * @returns {*}
 */
  export const getSimilarQuestions = (data: { word: string; pageNum: number,pageSize: number}) =>
    http({
      url: '/api/commonQuestion/v1/similar',
      data,
      method: 'post'
    })
 /**
 * 获取历史消息集
 * @param params
 * @returns {*}
 */
 export const apiGetHistorys = (data: { chatId: string; }) => {
  const menusStore = useMenusStore()
  const prefix = menusStore.isTianda ? '/tianda' : '/api'
  return http({
    url: `${prefix}/baseChat/v1/getHistory`,
    data,
    method: 'post'
  })
 }
 /**
 * 点赞点踩
 * @param params
 * @returns {*}
 */
 export const apiMessageMark = (data: {
  userId?: string,
  questionId: string
  answerId: string
  markType: number
  actType: number
  dislikeReasonList?: []
  userDescription?: string
}) =>
  http({
    url: '/api/baseChatMessageMark/v1/message/mark',
    data:{
      ...data,
      userId: getCookie().userId
    },
    method: 'post'
  })
 /**
 * 点赞点踩
 * @param params
 * @returns {*}
 */
 export const apiGetDislikeReasonList = () =>
  http({
    url: '/api/dislikeReason/public/v1/getDislikeReasonList',
    method: 'post'
  })




 /**
 * 操作手册树模版
 * @param params
 * @returns {*}
 */
 export const getOperatingTemplate = (data: { fileId: string | null,type:  string | null,segmentIds: any[] }) =>
  http({
    // url: '/test/baseChatTemplate/content/getRightTemplateContent',
    url: '/api/baseChatTemplate/content/getRightTemplateContent',
    method: 'post',
    data
  })

 /**
 * 操作手册树模版左测按钮
 * @param params
 * @returns {*}
 */
 export const getOperatingMenu =  (data: { fileId: string | null,type:  string | null,segmentIds: any[] }) =>
  http({
    // url: '/test/baseChatTemplate/content/getLeftMenuList',
    url: '/api/baseChatTemplate/content/getLeftMenuList',
    method: 'post',
    data
  })
/**
 * 获取切片
 */
export const apiRefsDocument = (data: { segmentIds: Array<string> }) => http({
    url: '/api/quote/v1/refsDocument',
    method: 'post',
    data
})
/**
 * 查询切片信息汇总接口
 */
export const refsSliceSummary = (data: { segmentIds: Array<string> }) => http({
  url: '/api/quote/v1/refsSliceSummary',
  method: 'post',
  data
})

/**
 * 查询原文文件列表
 */
export const getTemplateFileList = (data: { segmentIds: any[],type: string }) => http({
  // url: '/test/baseChatTemplate/content/getFileList',
  url: '/api/baseChatTemplate/content/getFileList',
  method: 'post',
  data
})

/**
 * 查询引导信息问题列表
 */
export const getQuestionList = () => http({
  url: '/api/guidanceInformation/v1/getQuestionList',
  method: 'post'
})
