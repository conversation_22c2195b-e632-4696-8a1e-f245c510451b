<template>
<div class="chat-robot">
  <!-- <div id="echartParent" style="height: calc(100vh - 44px); width: 600px; position: fixed; top:-9999px;border: 1px solid rgb(0, 0, 0); margin-right: 16px;">
    <div style="display: flex; justify-content: flex-end;">
      <a-button type="link" @click="closeEchart">关闭图表</a-button>
    </div>
    <div id="echart" style="width: 100%; height: 100%;">

    </div>
  </div> -->
  <div id="chatBody" class="chat-robot-main">
    <ConfigProvider :theme="theme">
      <index-header v-if="showHeader"/>
      <main-body />
    </ConfigProvider>
  </div>
  <index-footer />
</div>
</template>

<script setup>
import {ref } from 'vue'
import {Button as AButton, ConfigProvider} from 'ant-design-vue'
import { useChatGptProvide } from '@/views/chat/hooks/use-chat'
import { useRobot } from '@/views/chat-robot/hooks/use-robot'
import IndexHeader from './components/header.vue'
import MainBody from './components/main-body.vue'
import IndexFooter from '@/views/chat/components/footer.vue'
// import emitter from '@/utils/mitt'

useChatGptProvide()
const theme = {
  token: {
    colorPrimary: '#133ce8'
  }
}

const props = defineProps({
  showHeader: {
    type: Boolean,
    default: true
  }
})
const showHeader = ref(props.showHeader)
window.parent.postMessage({
  'command': 'initComponentFinish'// initComponentFinish
}, '*')
useRobot()

// emitter.on('showEchart', (echartConfig) => {
//   window.parent.postMessage({
//     'command': 'showEchart',
//     'echartConfig': echartConfig
//   }, '*')
// });

// function closeEchart(){
//   let dom = document.querySelector('#echartParent');
//   dom.style.position = 'fixed'
//   window.parent.postMessage({
//     'command': 'returnLastWidthChatRobot'
//   }, '*')
// }
</script>

<style lang="scss" scoped>
.chat-window {
  height: 100%;
  width: 100%;
  z-index: 1;
  top: 48px;
  right: 0px;
  border: 0.06rem solid var(--fill-0);
  border-radius: var(--border-radius-24) 0rem 0rem var(--border-radius-24);
  background: rgba(255, 255, 255, 0.85);
  box-sizing: border-box;
  backdrop-filter: blur(0.63rem);
  box-shadow: 0rem 0rem 2rem 0rem rgba(0, 0, 0, 0.3);
  position: absolute;
  touch-action: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  line-height: normal;
}

.chat-robot {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-radius: var(--border-radius-24) 0px 0px var(--border-radius-24);
  padding: 24px 24px 0 24px;
  background: rgba(255, 255, 255, 0.85);
  box-sizing: border-box;
  border: 1px solid var(--fill-0);
  backdrop-filter: blur(10px);
  box-shadow: 0px 0px 32px 0px rgba(0, 0, 0, 0.3);
  overflow-x: hidden;

  .chat-robot-main {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .footer{
    padding: unset;
  }
}
</style>
