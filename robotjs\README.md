

## ChatRobot

### 使用

1、引入：

```html
<script src="https://sppgpt.gcycloud.cn/chat_robot_js/index.js"></script>
```

2、创建聊天窗口对象：

```js
const chatRobot = new ChatRobot('聊天机器人界面地址', '用户Token', 根元素字体大小（默认16）)
```

3、唤起聊天窗口：

```js
chatRobot.show()
```

4、隐藏聊天窗口：

```js
chatRobot.hide()
```

5、刷新用户Token：

```js
chatRobot.refreshToken('用户Token')
```
### DEMO

```html
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

</head>

<body>
    <button id="show">显示窗口</button>
    <button id="hide">隐藏窗口</button>
</body>

<script src="https://sppgpt.gcycloud.cn/chat_robot_js/index.js"></script>
<script>
    const chatRobot = new ChatRobot('聊天机器人界面地址', '用户Token')

    // 显示聊天窗
    document.querySelector("#show").addEventListener("mousedown", ()=>{
        chatRobot.show()
    })

    // 隐藏聊天窗
    document.querySelector("#hide").addEventListener("mousedown", ()=>{
        chatRobot.hide()
    })
</script>

</html>

```
