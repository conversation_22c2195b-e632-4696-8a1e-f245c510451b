<template>
  <div class="base-swiper">
    <swiper
        :modules="modules"
        :slides-per-view="slidesPerView"
        :space-between="spaceBetween"
        :loop="loop"
        :autoplay="autoplay"
        :navigation="navigation"
        :pagination="pagination"
        :scrollbar="scrollbar"
        v-bind="$attrs"
        @swiper="onSwiper"
        @slider-change="onSliderChange"
    >
        <swiper-slide
          v-for="(item, index) in data"
          :key="index"
          :class="{'is-active': selectIndex === index}"
          :virtual-index="index"
          @click="onSwiperClick(item,index)">
            <slot :row="item"></slot>
        </swiper-slide>
    </swiper>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose } from 'vue'
// Import Swiper Vue.js components
import { Swiper, SwiperSlide } from 'swiper/vue'
// 引入 swiper 所需模块
import { Autoplay, Pagination, Navigation, Scrollbar, A11y, Virtual } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'

defineOptions({ name: 'BaseSwiper' })

interface Props {
  slidesPerView?: number | string;// 控制一次显示几张轮播图
  loop?: boolean; // 循环播放
  navigation?:boolean; // 定义左右切换箭头
  scrollbar?: any;// 滚动条
  pagination?:any;// 控制是否可以点击圆点指示器切换轮播
  spaceBetween?:number;// 每张轮播图之间的距离，该属性不可以和 margin 属性同时使用
  autoplay?:any;// 自动播放
  data?: any[];// 数据
}
withDefaults(defineProps<Props>(), {
  slidesPerView: 4,
  loop: false,
  pagination: false,
  scrollbar:false,
  navigation: true,
  spaceBetween: 8,
  autoplay:false,
  data: ()=> {
    return []
  }
})
const emits = defineEmits(['slidePrev','slideNext','onSliderChange','onSwiper','onSwiperClick'])
// 在 modules 加入要使用的模块
const modules = reactive([Autoplay, Pagination, Navigation, Scrollbar, A11y, Virtual])

// 自定义的数据
let useSwiper: any = null
// 初始化 swiper
const onSwiper = (swiper:any) => {
    useSwiper = swiper
    emits('onSwiper')
}
const selectIndex = ref(0)
const onSwiperClick = (val: any,index:number)=> {
  useSwiper.activeIndex =  index
  selectIndex.value = index
  useSwiper?.slideTo(index, 500)
  emits('onSwiperClick',val)
}
// 滑动事件
const onSliderChange = () => {
    console.log('slide change')
    emits('onSliderChange')
}

// 通过实例方法自定义上一个下一个事件
const prevEl = () => {
    useSwiper.slidePrev()
    emits('slidePrev')
}
const nextEl = () => {
    useSwiper.slideNext()
    emits('slideNext')
}

// 暴露方法，用于父组件调用
defineExpose({
    prevEl,
    nextEl
})
</script>


<style lang="scss" scoped>
.base-swiper {
  width: 100%;
  height: 100%;
  :deep(.swiper){
    .swiper-button-next,
    .swiper-button-prev {
      width: 44px;
      height: 44px;
      &::after{
        content: '';
      }
      &.swiper-button-disabled {
        display: none
      }
    }
    .swiper-button-prev {
      background: url('@/assets/images/arrow-left.png')  no-repeat center center;
      background-size: 100% auto;
    }
    .swiper-button-next {
      background: url('@/assets/images/arrow-right.png')  no-repeat center center;
      background-size: 100% auto;
    }
  }
}
</style>
